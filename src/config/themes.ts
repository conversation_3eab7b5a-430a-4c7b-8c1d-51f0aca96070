// 皮肤主题配置文件
// 定义不同皮肤的背景图片、背景颜色、边框颜色等属性

// 导入背景图片
import galaxy5 from '@/assets/img/galaxy5.jpg';
import galaxy4 from '@/assets/img/galaxy4.jpg';
import island from '@/assets/img/island.jpg';
import islandDark from '@/assets/img/island_dark.jpg';
import pinkImg from '@/assets/img/pink.jpg';
import yellowImg from '@/assets/img/yellow.jpg';
import greenImg from '@/assets/img/green.jpg';
import blueImg from '@/assets/img/blue.jpg';

export interface IThemeColors {
  // 主色调
  primaryColor: string;
  primaryColorLight: string;
  primaryColorMedium: string;
  primaryColorStrong: string;

  // 强调色
  accentColor: string;
  accentColorLight: string;
  accentColorMedium: string;
  accentColorStrong: string;

  // 背景色
  bgGlass: string;
  bgGlassHover: string;
  bgGlassPopup: string;
  borderGlass: string;
  borderAccent: string;

  // 阴影
  shadowSoft: string;
  shadowStrong: string;
  shadowAccent: string;

  // 其他颜色
  overlayDark: string;
  bgInput: string;
  bgInputFocus: string;
  placeholderColor: string;
  successColor: string;
  successColorLight: string;
  successColorMedium: string;
  successColorStrong: string;
  errorColor: string;
  errorColorLight: string;
  errorColorMedium: string;
  errorColorStrong: string;

  // 图标滤镜
  iconFilterPrimary: string;
  iconFilterAccent: string;
  iconFilterWhite: string;

  // 页面文字颜色（用于特定主题的深色字体）
  pageTextPrimary: string;
  pageTextSecondary: string;
  pageTextTertiary: string;
}

export interface IThemeConfig {
  id: string;
  name: string;
  backgroundImage: string;
  colors: IThemeColors;
}

// 默认主题（当前的赛博朋克风格）
export const defaultTheme: IThemeConfig = {
  id: 'cyberpunk',
  name: '赛博朋克',
  backgroundImage: galaxy5,
  colors: {
    // 主色调
    primaryColor: '#00bcd4',
    primaryColorLight: 'rgba(0, 188, 212, 0.1)',
    primaryColorMedium: 'rgba(0, 188, 212, 0.2)',
    primaryColorStrong: 'rgba(0, 188, 212, 0.3)',

    // 强调色
    accentColor: '#00ffff',
    accentColorLight: 'rgba(0, 255, 255, 0.1)',
    accentColorMedium: 'rgba(0, 255, 255, 0.2)',
    accentColorStrong: 'rgba(0, 255, 255, 0.3)',

    // 背景色
    bgGlass: 'rgba(30, 58, 138, 0.15)',
    bgGlassHover: 'rgba(30, 58, 138, 0.25)',
    bgGlassPopup: 'rgba(30, 58, 138, 0.35)',
    borderGlass: 'rgba(255, 255, 255, 0.2)',
    borderAccent: 'rgba(0, 255, 255, 0.3)',

    // 阴影
    shadowSoft: '0 8px 32px rgba(0, 0, 0, 0.2)',
    shadowStrong: '0 20px 60px rgba(0, 0, 0, 0.3)',
    shadowAccent: '0 0 20px rgba(0, 255, 255, 0.2)',

    // 其他颜色
    overlayDark: 'rgba(0, 0, 0, 0.3)',
    bgInput: 'rgba(255, 255, 255, 0.08)',
    bgInputFocus: 'rgba(255, 255, 255, 0.12)',
    successColor: '#22c55e',
    successColorLight: 'rgba(34, 197, 94, 0.1)',
    successColorMedium: 'rgba(34, 197, 94, 0.2)',
    successColorStrong: 'rgba(34, 197, 94, 0.3)',
    errorColor: '#ef4444',
    errorColorLight: 'rgba(239, 68, 68, 0.1)',
    errorColorMedium: 'rgba(239, 68, 68, 0.2)',
    errorColorStrong: 'rgba(239, 68, 68, 0.3)',

    // 图标滤镜 - 青色系
    iconFilterPrimary:
      'brightness(0) saturate(100%) invert(77%) sepia(93%) saturate(1352%) hue-rotate(169deg) brightness(97%) contrast(96%)',
    iconFilterAccent:
      'brightness(0) saturate(100%) invert(70%) sepia(98%) saturate(1000%) hue-rotate(180deg) brightness(100%) contrast(101%)',
    iconFilterWhite: 'brightness(0) invert(1)',

    // 页面文字颜色 - 保持白色
    pageTextPrimary: '#ffffff',
    pageTextSecondary: 'rgba(255, 255, 255, 0.9)',
    pageTextTertiary: 'rgba(255, 255, 255, 0.8)',

    // 输入框placeholder颜色
    placeholderColor: 'rgba(255, 255, 255, 0.5)',
  },
};

// 星空主题
export const starryTheme: IThemeConfig = {
  id: 'starry',
  name: '星空',
  backgroundImage: galaxy4,
  colors: {
    // 主色调 - 紫色系
    primaryColor: '#8b5cf6',
    primaryColorLight: 'rgba(139, 92, 246, 0.1)',
    primaryColorMedium: 'rgba(139, 92, 246, 0.2)',
    primaryColorStrong: 'rgba(139, 92, 246, 0.3)',

    // 强调色 - 亮紫色
    accentColor: '#a855f7',
    accentColorLight: 'rgba(168, 85, 247, 0.1)',
    accentColorMedium: 'rgba(168, 85, 247, 0.2)',
    accentColorStrong: 'rgba(168, 85, 247, 0.3)',

    // 背景色 - 深紫色调
    bgGlass: 'rgba(88, 28, 135, 0.15)',
    bgGlassHover: 'rgba(88, 28, 135, 0.25)',
    bgGlassPopup: 'rgba(88, 28, 135, 0.35)',
    borderGlass: 'rgba(255, 255, 255, 0.2)',
    borderAccent: 'rgba(168, 85, 247, 0.3)',

    // 阴影 - 紫色调
    shadowSoft: '0 8px 32px rgba(0, 0, 0, 0.2)',
    shadowStrong: '0 20px 60px rgba(0, 0, 0, 0.3)',
    shadowAccent: '0 0 20px rgba(168, 85, 247, 0.2)',

    // 其他颜色
    overlayDark: 'rgba(0, 0, 0, 0.3)',
    bgInput: 'rgba(255, 255, 255, 0.08)',
    bgInputFocus: 'rgba(255, 255, 255, 0.12)',
    successColor: '#22c55e',
    successColorLight: 'rgba(34, 197, 94, 0.1)',
    successColorMedium: 'rgba(34, 197, 94, 0.2)',
    successColorStrong: 'rgba(34, 197, 94, 0.3)',
    errorColor: '#ef4444',
    errorColorLight: 'rgba(239, 68, 68, 0.1)',
    errorColorMedium: 'rgba(239, 68, 68, 0.2)',
    errorColorStrong: 'rgba(239, 68, 68, 0.3)',

    // 图标滤镜 - 紫色系
    iconFilterPrimary:
      'brightness(0) saturate(100%) invert(60%) sepia(51%) saturate(2878%) hue-rotate(246deg) brightness(118%) contrast(119%)',
    iconFilterAccent:
      'brightness(0) saturate(100%) invert(55%) sepia(69%) saturate(2102%) hue-rotate(251deg) brightness(103%) contrast(107%)',
    iconFilterWhite: 'brightness(0) invert(1)',

    // 页面文字颜色 - 保持白色
    pageTextPrimary: '#ffffff',
    pageTextSecondary: 'rgba(255, 255, 255, 0.9)',
    pageTextTertiary: 'rgba(255, 255, 255, 0.8)',

    // 输入框placeholder颜色
    placeholderColor: 'rgba(255, 255, 255, 0.5)',
  },
};

// 海岛主题
export const islandTheme: IThemeConfig = {
  id: 'island',
  name: '海岛',
  backgroundImage: island,
  colors: {
    // 主色调 - 蓝绿色系
    primaryColor: '#06b6d4',
    primaryColorLight: 'rgba(6, 182, 212, 0.1)',
    primaryColorMedium: 'rgba(6, 182, 212, 0.2)',
    primaryColorStrong: 'rgba(6, 182, 212, 0.3)',

    // 强调色 - 翠绿色
    accentColor: '#10b981',
    accentColorLight: 'rgba(16, 185, 129, 0.1)',
    accentColorMedium: 'rgba(16, 185, 129, 0.2)',
    accentColorStrong: 'rgba(16, 185, 129, 0.3)',

    // 背景色 - 海洋蓝调
    bgGlass: 'rgba(14, 116, 144, 0.15)',
    bgGlassHover: 'rgba(14, 116, 144, 0.25)',
    bgGlassPopup: 'rgba(14, 116, 144, 0.35)',
    borderGlass: 'rgba(255, 255, 255, 0.2)',
    borderAccent: 'rgba(16, 185, 129, 0.3)',

    // 阴影 - 蓝绿调
    shadowSoft: '0 8px 32px rgba(0, 0, 0, 0.2)',
    shadowStrong: '0 20px 60px rgba(0, 0, 0, 0.3)',
    shadowAccent: '0 0 20px rgba(16, 185, 129, 0.2)',

    // 其他颜色
    overlayDark: 'rgba(0, 0, 0, 0.3)',
    bgInput: 'rgba(255, 255, 255, 0.08)',
    bgInputFocus: 'rgba(255, 255, 255, 0.12)',
    successColor: '#22c55e',
    successColorLight: 'rgba(34, 197, 94, 0.1)',
    successColorMedium: 'rgba(34, 197, 94, 0.2)',
    successColorStrong: 'rgba(34, 197, 94, 0.3)',
    errorColor: '#ef4444',
    errorColorLight: 'rgba(239, 68, 68, 0.1)',
    errorColorMedium: 'rgba(239, 68, 68, 0.2)',
    errorColorStrong: 'rgba(239, 68, 68, 0.3)',

    // 图标滤镜 - 蓝绿色系
    iconFilterPrimary:
      'brightness(0) saturate(100%) invert(77%) sepia(93%) saturate(1352%) hue-rotate(169deg) brightness(97%) contrast(96%)',
    iconFilterAccent:
      'brightness(0) saturate(100%) invert(70%) sepia(98%) saturate(1000%) hue-rotate(180deg) brightness(100%) contrast(101%)',
    iconFilterWhite: 'brightness(0) invert(1)',

    // 页面文字颜色 - 使用深色（调浅）
    pageTextPrimary: '#6b7280',
    pageTextSecondary: '#9ca3af',
    pageTextTertiary: '#d1d5db',

    // 输入框placeholder颜色 - 深色
    placeholderColor: '#9ca3af',
  },
};

// 暗夜海岛主题
export const darkIslandTheme: IThemeConfig = {
  id: 'dark-island',
  name: '暗夜海岛',
  backgroundImage: islandDark,
  colors: {
    // 主色调 - 深蓝色系
    primaryColor: '#1e40af',
    primaryColorLight: 'rgba(30, 64, 175, 0.1)',
    primaryColorMedium: 'rgba(30, 64, 175, 0.2)',
    primaryColorStrong: 'rgba(30, 64, 175, 0.3)',

    // 强调色 - 深青色
    accentColor: '#0891b2',
    accentColorLight: 'rgba(8, 145, 178, 0.1)',
    accentColorMedium: 'rgba(8, 145, 178, 0.2)',
    accentColorStrong: 'rgba(8, 145, 178, 0.3)',

    // 背景色 - 深海蓝调
    bgGlass: 'rgba(30, 58, 138, 0.15)',
    bgGlassHover: 'rgba(30, 58, 138, 0.25)',
    bgGlassPopup: 'rgba(30, 58, 138, 0.35)',
    borderGlass: 'rgba(255, 255, 255, 0.2)',
    borderAccent: 'rgba(8, 145, 178, 0.3)',

    // 阴影 - 深蓝调
    shadowSoft: '0 8px 32px rgba(0, 0, 0, 0.2)',
    shadowStrong: '0 20px 60px rgba(0, 0, 0, 0.3)',
    shadowAccent: '0 0 20px rgba(8, 145, 178, 0.2)',

    // 其他颜色
    overlayDark: 'rgba(0, 0, 0, 0.3)',
    bgInput: 'rgba(255, 255, 255, 0.08)',
    bgInputFocus: 'rgba(255, 255, 255, 0.12)',
    successColor: '#22c55e',
    successColorLight: 'rgba(34, 197, 94, 0.1)',
    successColorMedium: 'rgba(34, 197, 94, 0.2)',
    successColorStrong: 'rgba(34, 197, 94, 0.3)',
    errorColor: '#ef4444',
    errorColorLight: 'rgba(239, 68, 68, 0.1)',
    errorColorMedium: 'rgba(239, 68, 68, 0.2)',
    errorColorStrong: 'rgba(239, 68, 68, 0.3)',

    // 图标滤镜 - 深蓝色系
    iconFilterPrimary:
      'brightness(0) saturate(100%) invert(26%) sepia(89%) saturate(1583%) hue-rotate(213deg) brightness(87%) contrast(138%)',
    iconFilterAccent:
      'brightness(0) saturate(100%) invert(35%) sepia(91%) saturate(1689%) hue-rotate(213deg) brightness(99%) contrast(107%)',
    iconFilterWhite: 'brightness(0) invert(1)',

    // 页面文字颜色 - 保持白色
    pageTextPrimary: '#ffffff',
    pageTextSecondary: 'rgba(255, 255, 255, 0.9)',
    pageTextTertiary: 'rgba(255, 255, 255, 0.8)',

    // 输入框placeholder颜色
    placeholderColor: 'rgba(255, 255, 255, 0.5)',
  },
};

// 粉色主题
export const pinkTheme: IThemeConfig = {
  id: 'pink',
  name: '粉色梦境',
  backgroundImage: pinkImg,
  colors: {
    // 主色调 - 粉色系
    primaryColor: '#ec4899',
    primaryColorLight: 'rgba(236, 72, 153, 0.1)',
    primaryColorMedium: 'rgba(236, 72, 153, 0.2)',
    primaryColorStrong: 'rgba(236, 72, 153, 0.3)',

    // 强调色 - 亮粉色
    accentColor: '#f472b6',
    accentColorLight: 'rgba(244, 114, 182, 0.1)',
    accentColorMedium: 'rgba(244, 114, 182, 0.2)',
    accentColorStrong: 'rgba(244, 114, 182, 0.3)',

    // 背景色 - 深粉色调
    bgGlass: 'rgba(131, 24, 67, 0.15)',
    bgGlassHover: 'rgba(131, 24, 67, 0.25)',
    bgGlassPopup: 'rgba(131, 24, 67, 0.35)',
    borderGlass: 'rgba(255, 255, 255, 0.2)',
    borderAccent: 'rgba(244, 114, 182, 0.3)',

    // 阴影 - 粉色调
    shadowSoft: '0 8px 32px rgba(0, 0, 0, 0.2)',
    shadowStrong: '0 20px 60px rgba(0, 0, 0, 0.3)',
    shadowAccent: '0 0 20px rgba(244, 114, 182, 0.2)',

    // 其他颜色
    overlayDark: 'rgba(0, 0, 0, 0.3)',
    bgInput: 'rgba(255, 255, 255, 0.08)',
    bgInputFocus: 'rgba(255, 255, 255, 0.12)',
    successColor: '#22c55e',
    successColorLight: 'rgba(34, 197, 94, 0.1)',
    successColorMedium: 'rgba(34, 197, 94, 0.2)',
    successColorStrong: 'rgba(34, 197, 94, 0.3)',
    errorColor: '#ef4444',
    errorColorLight: 'rgba(239, 68, 68, 0.1)',
    errorColorMedium: 'rgba(239, 68, 68, 0.2)',
    errorColorStrong: 'rgba(239, 68, 68, 0.3)',

    // 图标滤镜 - 粉色系
    iconFilterPrimary:
      'brightness(0) saturate(100%) invert(51%) sepia(98%) saturate(1206%) hue-rotate(314deg) brightness(97%) contrast(97%)',
    iconFilterAccent:
      'brightness(0) saturate(100%) invert(64%) sepia(88%) saturate(1552%) hue-rotate(316deg) brightness(99%) contrast(101%)',
    iconFilterWhite: 'brightness(0) invert(1)',

    // 页面文字颜色 - 使用深色（调浅）
    pageTextPrimary: '#6b7280',
    pageTextSecondary: '#9ca3af',
    pageTextTertiary: '#d1d5db',

    // 输入框placeholder颜色 - 深色
    placeholderColor: '#9ca3af',
  },
};

// 黄色主题
export const yellowTheme: IThemeConfig = {
  id: 'yellow',
  name: '金色阳光',
  backgroundImage: yellowImg,
  colors: {
    // 主色调 - 黄色系
    primaryColor: '#eab308',
    primaryColorLight: 'rgba(234, 179, 8, 0.1)',
    primaryColorMedium: 'rgba(234, 179, 8, 0.2)',
    primaryColorStrong: 'rgba(234, 179, 8, 0.3)',

    // 强调色 - 亮黄色
    accentColor: '#facc15',
    accentColorLight: 'rgba(250, 204, 21, 0.1)',
    accentColorMedium: 'rgba(250, 204, 21, 0.2)',
    accentColorStrong: 'rgba(250, 204, 21, 0.3)',

    // 背景色 - 深黄色调
    bgGlass: 'rgba(120, 53, 15, 0.15)',
    bgGlassHover: 'rgba(120, 53, 15, 0.25)',
    bgGlassPopup: 'rgba(120, 53, 15, 0.35)',
    borderGlass: 'rgba(255, 255, 255, 0.2)',
    borderAccent: 'rgba(250, 204, 21, 0.3)',

    // 阴影 - 黄色调
    shadowSoft: '0 8px 32px rgba(0, 0, 0, 0.2)',
    shadowStrong: '0 20px 60px rgba(0, 0, 0, 0.3)',
    shadowAccent: '0 0 20px rgba(250, 204, 21, 0.2)',

    // 其他颜色
    overlayDark: 'rgba(0, 0, 0, 0.3)',
    bgInput: 'rgba(255, 255, 255, 0.08)',
    bgInputFocus: 'rgba(255, 255, 255, 0.12)',
    successColor: '#22c55e',
    successColorLight: 'rgba(34, 197, 94, 0.1)',
    successColorMedium: 'rgba(34, 197, 94, 0.2)',
    successColorStrong: 'rgba(34, 197, 94, 0.3)',
    errorColor: '#ef4444',
    errorColorLight: 'rgba(239, 68, 68, 0.1)',
    errorColorMedium: 'rgba(239, 68, 68, 0.2)',
    errorColorStrong: 'rgba(239, 68, 68, 0.3)',

    // 图标滤镜 - 黄色系
    iconFilterPrimary:
      'brightness(0) saturate(100%) invert(64%) sepia(78%) saturate(1142%) hue-rotate(6deg) brightness(101%) contrast(102%)',
    iconFilterAccent:
      'brightness(0) saturate(100%) invert(78%) sepia(61%) saturate(1142%) hue-rotate(6deg) brightness(101%) contrast(102%)',
    iconFilterWhite: 'brightness(0) invert(1)',

    // 页面文字颜色 - 使用深色（调浅）
    pageTextPrimary: '#6b7280',
    pageTextSecondary: '#9ca3af',
    pageTextTertiary: '#d1d5db',

    // 输入框placeholder颜色 - 深色
    placeholderColor: '#9ca3af',
  },
};

// 绿色主题
export const greenTheme: IThemeConfig = {
  id: 'green',
  name: '翠绿森林',
  backgroundImage: greenImg,
  colors: {
    // 主色调 - 绿色系
    primaryColor: '#16a34a',
    primaryColorLight: 'rgba(22, 163, 74, 0.1)',
    primaryColorMedium: 'rgba(22, 163, 74, 0.2)',
    primaryColorStrong: 'rgba(22, 163, 74, 0.3)',

    // 强调色 - 亮绿色
    accentColor: '#22c55e',
    accentColorLight: 'rgba(34, 197, 94, 0.1)',
    accentColorMedium: 'rgba(34, 197, 94, 0.2)',
    accentColorStrong: 'rgba(34, 197, 94, 0.3)',

    // 背景色 - 深绿色调
    bgGlass: 'rgba(20, 83, 45, 0.15)',
    bgGlassHover: 'rgba(20, 83, 45, 0.25)',
    bgGlassPopup: 'rgba(20, 83, 45, 0.35)',
    borderGlass: 'rgba(255, 255, 255, 0.2)',
    borderAccent: 'rgba(34, 197, 94, 0.3)',

    // 阴影 - 绿色调
    shadowSoft: '0 8px 32px rgba(0, 0, 0, 0.2)',
    shadowStrong: '0 20px 60px rgba(0, 0, 0, 0.3)',
    shadowAccent: '0 0 20px rgba(34, 197, 94, 0.2)',

    // 其他颜色
    overlayDark: 'rgba(0, 0, 0, 0.3)',
    bgInput: 'rgba(255, 255, 255, 0.08)',
    bgInputFocus: 'rgba(255, 255, 255, 0.12)',
    successColor: '#22c55e',
    successColorLight: 'rgba(34, 197, 94, 0.1)',
    successColorMedium: 'rgba(34, 197, 94, 0.2)',
    successColorStrong: 'rgba(34, 197, 94, 0.3)',
    errorColor: '#ef4444',
    errorColorLight: 'rgba(239, 68, 68, 0.1)',
    errorColorMedium: 'rgba(239, 68, 68, 0.2)',
    errorColorStrong: 'rgba(239, 68, 68, 0.3)',

    // 图标滤镜 - 绿色系
    iconFilterPrimary:
      'brightness(0) saturate(100%) invert(42%) sepia(93%) saturate(1352%) hue-rotate(87deg) brightness(119%) contrast(119%)',
    iconFilterAccent:
      'brightness(0) saturate(100%) invert(64%) sepia(98%) saturate(1000%) hue-rotate(87deg) brightness(100%) contrast(101%)',
    iconFilterWhite: 'brightness(0) invert(1)',

    // 页面文字颜色 - 使用深色（调浅）
    pageTextPrimary: '#6b7280',
    pageTextSecondary: '#9ca3af',
    pageTextTertiary: '#d1d5db',

    // 输入框placeholder颜色 - 深色
    placeholderColor: '#9ca3af',
  },
};

// 蓝色主题
export const blueTheme: IThemeConfig = {
  id: 'blue',
  name: '深海蓝调',
  backgroundImage: blueImg,
  colors: {
    // 主色调 - 蓝色系
    primaryColor: '#2563eb',
    primaryColorLight: 'rgba(37, 99, 235, 0.1)',
    primaryColorMedium: 'rgba(37, 99, 235, 0.2)',
    primaryColorStrong: 'rgba(37, 99, 235, 0.3)',

    // 强调色 - 亮蓝色
    accentColor: '#3b82f6',
    accentColorLight: 'rgba(59, 130, 246, 0.1)',
    accentColorMedium: 'rgba(59, 130, 246, 0.2)',
    accentColorStrong: 'rgba(59, 130, 246, 0.3)',

    // 背景色 - 深蓝色调
    bgGlass: 'rgba(30, 58, 138, 0.15)',
    bgGlassHover: 'rgba(30, 58, 138, 0.25)',
    bgGlassPopup: 'rgba(30, 58, 138, 0.35)',
    borderGlass: 'rgba(255, 255, 255, 0.2)',
    borderAccent: 'rgba(59, 130, 246, 0.3)',

    // 阴影 - 蓝色调
    shadowSoft: '0 8px 32px rgba(0, 0, 0, 0.2)',
    shadowStrong: '0 20px 60px rgba(0, 0, 0, 0.3)',
    shadowAccent: '0 0 20px rgba(59, 130, 246, 0.2)',

    // 其他颜色
    overlayDark: 'rgba(0, 0, 0, 0.3)',
    bgInput: 'rgba(255, 255, 255, 0.08)',
    bgInputFocus: 'rgba(255, 255, 255, 0.12)',
    successColor: '#22c55e',
    successColorLight: 'rgba(34, 197, 94, 0.1)',
    successColorMedium: 'rgba(34, 197, 94, 0.2)',
    successColorStrong: 'rgba(34, 197, 94, 0.3)',
    errorColor: '#ef4444',
    errorColorLight: 'rgba(239, 68, 68, 0.1)',
    errorColorMedium: 'rgba(239, 68, 68, 0.2)',
    errorColorStrong: 'rgba(239, 68, 68, 0.3)',

    // 图标滤镜 - 蓝色系
    iconFilterPrimary:
      'brightness(0) saturate(100%) invert(35%) sepia(91%) saturate(1689%) hue-rotate(213deg) brightness(99%) contrast(107%)',
    iconFilterAccent:
      'brightness(0) saturate(100%) invert(50%) sepia(96%) saturate(2044%) hue-rotate(213deg) brightness(99%) contrast(107%)',
    iconFilterWhite: 'brightness(0) invert(1)',

    // 页面文字颜色 - 使用深色（调浅）
    pageTextPrimary: '#6b7280',
    pageTextSecondary: '#9ca3af',
    pageTextTertiary: '#d1d5db',

    // 输入框placeholder颜色 - 深色
    placeholderColor: '#9ca3af',
  },
};

// 所有可用主题
export const availableThemes: IThemeConfig[] = [
  defaultTheme,
  starryTheme,
  islandTheme,
  darkIslandTheme,
  pinkTheme,
  yellowTheme,
  greenTheme,
  blueTheme,
];

// 根据ID获取主题
export function getThemeById(id: string): IThemeConfig | undefined {
  return availableThemes.find((theme) => theme.id === id);
}

// 获取默认主题
export function getDefaultTheme(): IThemeConfig {
  return defaultTheme;
}
