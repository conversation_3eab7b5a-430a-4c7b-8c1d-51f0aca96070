<template>
  <div class="info-section" :class="{ expanded: isInfoExpanded, editing: isEditingBasicInfo }">
    <div class="section-header">
      <span class="section-icon">📋</span>
      <span class="section-title">个人信息</span>
      <div class="section-actions">
        <button class="section-tts-btn" title="朗读内容" @click="handleTtsClick">
          <img src="@/assets/icon/trumpet.png" alt="朗读" class="section-tts-icon" />
        </button>
        <!-- <button class="section-mic-btn" title="语音" @click="handleBasicInfoMicClick">
          <img src="@/assets/icon/mic.png" alt="语音" class="section-mic-icon" />
        </button> -->
        <button class="section-edit-btn" title="编辑" @click="handleSectionArrowClick">
          <img src="@/assets/icon/edit.png" alt="编辑" class="section-edit-icon" />
        </button>
      </div>
    </div>
    <div class="section-content" :class="{ expanded: isInfoExpanded }" @click="handleInfoContentClick">
      <div v-if="loading" class="loading-text">加载中...</div>
      <div v-else-if="personDetail" class="info-content">
        <!-- 编辑模式 -->
        <div v-if="isEditingBasicInfo" class="edit-mode">
          <div class="voice-input-wrapper">
            <!-- 麦克风按钮在多行输入框左上角 -->
            <div class="voice-toggle-inner" :class="{ breathing: isRecording }" @click="handleVoiceButtonClick">
              <i class="iconfont icon-microphone" class-prefix="icon"></i>
            </div>
            <textarea
              ref="basicInfoTextarea"
              v-model="basicInfoEditValue"
              class="edit-textarea"
              placeholder="请输入个人信息..."
              rows="6"
              @input="handleTextareaInput"
              @keydown.esc="handleBasicInfoEditCancel"
            />
          </div>
          <div class="edit-actions">
            <button class="save-btn" @click="handleBasicInfoEditComplete">保存</button>
            <button class="cancel-btn" @click="handleBasicInfoEditCancel">取消</button>
          </div>
        </div>
        <!-- 显示模式 -->
        <div v-else class="info-text-container">
          <p class="info-text" :class="{ collapsed: !isInfoExpanded }">
            {{ personDetail.profile_summary || '暂无个人信息' }}
          </p>
        </div>
      </div>
      <div v-else class="info-content">
        <div class="info-text-container">
          <p class="info-text">暂无个人信息</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onBeforeUnmount } from 'vue';
import type { IPersonDetail } from '@/apis/memory';
import { updatePerson } from '@/apis/relation';
import { showFailToast, showSuccessToast, showToast } from 'vant';
import { getStreamAsr } from '@/apis/chat';
import Recorder from 'recorder-realtime';
import { generateRandomString } from '@/utils';
import { debounce } from 'lodash-es';
import { useAudioQueue } from '@/pages/Chat/useAudioPlayer';

// Props定义
interface IProps {
  personDetail: IPersonDetail | null;
  personId: string;
  userId: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  basicInfoMicClick: [];
  personUpdated: [personDetail: IPersonDetail];
}>();

// TTS相关
const { play, stop, isCurrentAudioPlaying, audioStatus } = useAudioQueue();
const isTtsPlaying = ref(false);
const ttsId = 'info-section-tts';

// 响应式数据
const isInfoExpanded = ref(false);
const isEditingBasicInfo = ref(false);
const basicInfoEditValue = ref('');
const loading = ref(false);

// 语音录音相关变量
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let timerId: ReturnType<typeof setTimeout> | null = null;
let mediaStream: MediaStream | null = null;

// 语音录音响应式数据
const micPermission = ref(false);
const sessionId = ref('');
const lastVoiceText = ref(''); // 上次语音识别的文字，用于增量更新
const audioBufferIndex = ref(0);
const lastBuffer = ref();
const voiceMessage = ref('');
const isRecording = ref(false);
const basicInfoTextarea = ref<HTMLTextAreaElement>();

// 切换个人信息展开/收起状态
const toggleInfoExpanded = () => {
  isInfoExpanded.value = !isInfoExpanded.value;
};

// 处理个人信息内容点击事件 - 区分点击展开和复制操作
const handleInfoContentClick = () => {
  // 如果点击的是编辑模式的textarea，不触发展开/收起
  if (isEditingBasicInfo.value) {
    return;
  }

  // 检查是否是文本选择操作（复制操作）
  const selection = window.getSelection();
  if (selection && selection.toString().length > 0) {
    // 有文本被选中，这是复制操作，不触发展开/收起
    return;
  }

  // 否则触发展开/收起
  toggleInfoExpanded();
};

// 在光标位置插入文字的工具函数（针对textarea元素）
const insertTextAtCursor = (newText: string) => {
  if (!basicInfoTextarea.value) return;

  const inputElement = basicInfoTextarea.value;
  const start = inputElement.selectionStart || 0;
  const end = inputElement.selectionEnd || 0;
  const currentValue = basicInfoEditValue.value;

  // 在光标位置插入新文字
  const newValue = currentValue.slice(0, start) + newText + currentValue.slice(end);
  basicInfoEditValue.value = newValue;

  // 更新光标位置到插入文字的末尾
  const newCursorPosition = start + newText.length;

  // 使用 nextTick 确保 DOM 更新后再设置光标位置
  void nextTick(() => {
    inputElement.setSelectionRange(newCursorPosition, newCursorPosition);
    inputElement.focus();
  });
};

// 语音录音相关方法
// 设置麦克风权限
async function setMicPermission() {
  try {
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
}

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    showToast('录音失败，浏览器不支持录音功能');
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  recorder.onstart = () => {};

  recorder.onstreamerror = () => {
    showToast('录音失败');
    cancelRecording();
  };

  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      const streamData = await getStreamAsr({
        sessionId: sessionId.value,
        format: 'pcm',
        sampleRate: 16000,
        index: audioBufferIndex.value,
        data: data.buffer,
      });
      if (
        streamData.data.full_text &&
        streamData.data.full_text.trim() !== '' &&
        streamData.data.full_text !== lastVoiceText.value
      ) {
        // 计算新增的文字部分
        const newText = streamData.data.full_text;
        const previousText = lastVoiceText.value;

        // 如果新文字包含之前的文字，只插入新增部分
        let textToInsert = newText;
        if (previousText && newText.startsWith(previousText)) {
          textToInsert = newText.slice(previousText.length);
        }

        // 在光标位置插入新文字
        if (textToInsert) {
          insertTextAtCursor(textToInsert);
        }

        lastVoiceText.value = newText;
        voiceMessage.value = newText;
        await autoStopTimeout();
      }
    }
  };
};

// 开始录音
async function startRecording() {
  if (isRecording.value) {
    await stopRecording();
    return;
  }

  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    // 重置语音识别状态
    lastVoiceText.value = '';
    voiceMessage.value = '';
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopRecording();
    }, 1000 * 60);
  }
}

// 两秒不说话自动停止
const autoStopTimeout = debounce(async () => {
  await stopRecording();
}, 2000);

// 释放麦克风资源
function releaseMicrophoneResources() {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => {
      track.stop();
    });
    mediaStream = null;
    micPermission.value = false;
  }
}

// 取消录音
function cancelRecording() {
  if (recorder) {
    recorder.stop();
  }
  isRecording.value = false;
  voiceMessage.value = '';
  releaseMicrophoneResources();
}

// 结束录音
async function stopRecording() {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });
  if (voiceMessage.value) {
    // 语音识别完成，文字已经通过 insertTextAtCursor 插入到光标位置
    // 不需要再次设置 basicInfoEditValue.value，避免覆盖用户可能的编辑
    console.log('📤 [InfoSection] 语音识别完成，文字已插入到光标位置:', voiceMessage.value);
  } else {
    showToast('录音解析为空，请重新录制~');
  }
  // 清空语音消息和上次识别文字
  voiceMessage.value = '';
  lastVoiceText.value = '';
}

// 处理语音按钮点击
const handleVoiceButtonClick = async () => {
  await startRecording();
};

// TTS朗读处理
const handleTtsClick = () => {
  if (isCurrentAudioPlaying(ttsId)) {
    stop();
    isTtsPlaying.value = false;
  } else {
    // 构建朗读内容：读出info-text区域展示内容
    const ttsContent = props.personDetail?.profile_summary || '暂无个人信息';

    if (ttsContent.trim()) {
      isTtsPlaying.value = true;
      play({
        id: ttsId,
        text: ttsContent,
        type: 'manualPlay',
      });

      // 监听播放状态变化
      const checkPlayingStatus = () => {
        if (!isCurrentAudioPlaying(ttsId) && audioStatus.value === 'completed') {
          isTtsPlaying.value = false;
        } else {
          setTimeout(checkPlayingStatus, 100);
        }
      };
      checkPlayingStatus();
    }
  }
};

// 处理section-arrow点击事件 - 个人信息
const handleSectionArrowClick = () => {
  isEditingBasicInfo.value = true;
  basicInfoEditValue.value = props.personDetail?.profile_summary || '';

  // 进入编辑模式时自动展开
  isInfoExpanded.value = true;

  // 使用nextTick确保DOM更新后再聚焦和调整高度
  void nextTick(() => {
    const textarea = document.querySelector('.info-section .edit-textarea') as HTMLTextAreaElement;
    if (textarea) {
      textarea.focus();
      // 自动调整textarea高度以适应内容
      autoResizeTextarea(textarea);
    }
  });
};

// 自动调整textarea高度的函数
const autoResizeTextarea = (textarea: HTMLTextAreaElement) => {
  // 重置高度以获取正确的scrollHeight
  textarea.style.height = 'auto';
  // 设置最小高度
  const minHeight = 120;
  // 计算内容高度，至少为最小高度
  const contentHeight = Math.max(textarea.scrollHeight, minHeight);
  // 设置新高度
  textarea.style.height = `${contentHeight}px`;
};

// 处理textarea输入事件，实时调整高度
const handleTextareaInput = (event: Event) => {
  const textarea = event.target as HTMLTextAreaElement;
  if (textarea) {
    autoResizeTextarea(textarea);
  }
};

// 处理个人信息编辑完成
const handleBasicInfoEditComplete = async () => {
  if (!props.personDetail) return;

  try {
    const aliases = props.personDetail.aliases || '';
    const submitAliases = aliases === '' ? '' : aliases;

    const response = await updatePerson(props.personDetail.person_id, {
      user_id: props.userId,
      canonical_name: props.personDetail.canonical_name,
      aliases: submitAliases,
      relationships: props.personDetail.relationships as string[],
      profile_summary: basicInfoEditValue.value.trim(),
      key_attributes: props.personDetail.key_attributes as Record<string, unknown>,
      is_user: props.personDetail.is_user,
      avatar: props.personDetail.avatar,
    });

    if (response && response.result === 'success') {
      // 更新本地数据
      const updatedPersonDetail = {
        ...props.personDetail,
        profile_summary: basicInfoEditValue.value.trim(),
      };
      emit('personUpdated', updatedPersonDetail);
      showSuccessToast('个人信息更新成功');
      // 保存成功后退出编辑模式
      isEditingBasicInfo.value = false;
    } else {
      showFailToast('个人信息更新失败');
    }
  } catch (error) {
    console.error('更新个人信息失败:', error);
    showFailToast('个人信息更新失败');
  }
};

// 处理个人信息编辑取消
const handleBasicInfoEditCancel = () => {
  isEditingBasicInfo.value = false;
  // 恢复原始值
  basicInfoEditValue.value = props.personDetail?.profile_summary || '';
};

// 组件卸载时释放麦克风资源
onBeforeUnmount(() => {
  if (isRecording.value) {
    if (recorder) {
      recorder.stop();
    }
    isRecording.value = false;
  }
  releaseMicrophoneResources();
});
</script>

<style lang="scss" scoped>
.info-section {
  border: none;
  border-radius: 16px;
  padding: 22px;
  margin-top: 24px;
  background: var(--primary-color-light);
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  display: flex;
  flex-direction: column;
}

// 个人信息展开/收起控制
.info-section .section-content {
  cursor: pointer;
  transition: max-height 0.3s ease;

  &:not(.expanded) {
    max-height: 200px;
    overflow: hidden;
  }

  &.expanded {
    max-height: none;
    overflow: visible;
  }
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;

  .section-icon {
    font-size: 32px;
  }

  .section-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 34px;
    font-weight: 600;
    flex: 1;
  }

  .section-actions {
    display: flex;
    align-items: center;
    gap: 14px;
  }

  .section-edit-btn,
  .section-mic-btn,
  .section-tts-btn {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: var(--primary-color-light);
      transform: translateY(-2px);
      box-shadow: var(--shadow-accent);
    }

    .section-edit-icon,
    .section-mic-icon,
    .section-tts-icon {
      width: 18px;
      height: 18px;
      filter: var(--icon-filter-primary);
    }
  }
}

.section-content {
  color: var(--text-primary);
  line-height: 1.6;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.loading-text {
  color: rgba(255, 255, 255, 1);
  font-style: italic;
  text-align: center;
  padding: 10px 0;
  font-size: 32px;
}

.info-content {
  .info-text-container {
    .info-text {
      margin: 0 0 16px 0;
      font-size: 32px;
      line-height: 1.6;
      transition: all 0.3s ease;

      &.collapsed {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

// 编辑模式样式
.edit-mode {
  .voice-input-wrapper {
    position: relative;
    width: 100%;

    .voice-toggle-inner {
      position: absolute;
      left: 16px;
      top: 40px; // 与第一行文字中心对齐
      transform: translateY(-50%);
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;
      background: var(--bg-glass);
      border: 2px solid var(--border-accent);
      backdrop-filter: blur(20px);
      transition: all 0.3s ease;
      z-index: 10;

      &.breathing {
        animation: breathing 2s ease-in-out infinite;
      }

      .iconfont {
        font-size: 24px;
        color: var(--text-primary);
      }
    }

    .edit-textarea {
      width: 100%;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 12px;
      padding: 14px 16px 14px 76px; // 左侧留出更多空间给更大的语音按钮
      color: rgba(255, 255, 255, 0.9);
      font-size: 32px;
      box-sizing: border-box;
      backdrop-filter: blur(10px);
      transition: all 0.2s ease;
      resize: vertical;
      min-height: 120px;
      max-height: 400px;
      font-family: inherit;
      line-height: 1.6;
      overflow-y: auto;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
      }
    }
  }

  .edit-actions {
    display: flex;
    gap: 12px;
    margin-top: 16px;

    .save-btn,
    .cancel-btn {
      padding: 12px 24px;
      border-radius: 12px;
      font-size: 28px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid;

      &.save-btn {
        background: rgba(0, 188, 212, 0.2);
        border-color: #00bcd4;
        color: #00bcd4;

        &:hover {
          background: rgba(0, 188, 212, 0.3);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
        }
      }

      &.cancel-btn {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.8);

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
        }
      }
    }
  }
}

// 编辑模式下的section展开样式
.info-section.editing {
  .section-content {
    max-height: none !important;
    overflow: visible !important;
  }
}
</style>
