<template>
  <div class="lifestyle-container">
    <!-- 去过&想去 -->
    <div class="travel-section">
      <div class="section-header">
        <span class="section-icon">✈️</span>
        <span class="section-title">去过&想去</span>
        <div class="section-actions">
          <button class="section-tts-btn" title="朗读内容" @click="handleTravelTtsClick">
            <img src="@/assets/icon/trumpet.png" alt="朗读" class="section-tts-icon" />
          </button>
          <!-- <button class="section-mic-btn" title="语音" @click="handleTravelMicClick">
            <img src="@/assets/icon/mic.png" alt="语音" class="section-mic-icon" />
          </button> -->
          <button class="section-edit-btn" title="编辑" @click="handleTravelSectionArrowClick">
            <img src="@/assets/icon/edit.png" alt="编辑" class="section-edit-icon" />
          </button>
        </div>
      </div>
      <div class="section-content">
        <!-- 编辑模式 -->
        <div v-if="showTravelEdit" class="edit-list-container">
          <div v-for="(_, index) in travelEditValues" :key="index" class="edit-item-container">
            <div class="voice-input-wrapper">
              <!-- 麦克风按钮在多行输入框左上角 -->
              <div
                class="voice-toggle-inner"
                :class="{ breathing: isRecording && currentVoiceIndex === index }"
                @click="() => handleVoiceButtonClick('travel', index)"
              >
                <i class="iconfont icon-microphone" class-prefix="icon"></i>
              </div>
              <textarea
                :ref="(el) => setTravelTextareaRef(el, index)"
                v-model="travelEditValues[index]"
                class="attribute-value auto-resize"
                :placeholder="`旅行地点 ${index + 1}`"
                rows="1"
                @input="autoResize"
              ></textarea>
            </div>
          </div>
          <!-- 空白输入框用于添加新项 -->
          <div class="edit-item-container">
            <div class="voice-input-wrapper">
              <!-- 麦克风按钮在多行输入框左上角 -->
              <div
                class="voice-toggle-inner"
                :class="{ breathing: isRecording && currentVoiceIndex === -1 }"
                @click="() => handleVoiceButtonClick('travel', -1)"
              >
                <i class="iconfont icon-microphone" class-prefix="icon"></i>
              </div>
              <textarea
                v-model="newTravelItem"
                class="attribute-value auto-resize"
                placeholder="添加新的旅行地点"
                rows="1"
                @input="autoResize"
              ></textarea>
            </div>
          </div>
          <div class="edit-actions">
            <button class="save-btn" @click="handleTravelEditComplete">保存</button>
            <button class="cancel-btn" @click="handleTravelEditCancel">取消</button>
          </div>
        </div>
        <!-- 显示模式 -->
        <div v-else class="travel-content">
          <div v-if="getTravelHistory()" class="travel-info">
            <div v-for="(location, index) in getTravelHistoryList()" :key="index" class="travel-item">
              <!-- 单个item编辑模式 -->
              <div v-if="editingTravelIndex === index" class="item-edit-mode">
                <div class="voice-input-wrapper">
                  <!-- 麦克风按钮在多行输入框左上角 -->
                  <div
                    class="voice-toggle-inner"
                    :class="{ breathing: isRecording && currentVoiceIndex === index }"
                    @click="() => handleVoiceButtonClick('travel', index)"
                  >
                    <i class="iconfont icon-microphone" class-prefix="icon"></i>
                  </div>
                  <textarea
                    v-model="tempTravelEditValue"
                    class="item-edit-input auto-resize"
                    :placeholder="`旅行地点 ${index + 1}`"
                    rows="1"
                    @input="autoResize"
                  ></textarea>
                </div>
                <div class="item-edit-actions">
                  <button class="item-save-btn" @click="handleTravelItemEditComplete(index)">保存</button>
                  <button class="item-cancel-btn" @click="handleTravelItemEditCancel">取消</button>
                </div>
              </div>
              <!-- 显示模式 -->
              <div v-else class="item-display-mode" @click="handleTravelItemEdit(index, location)">
                {{ location }}
                <button class="delete-item-btn" title="删除" @click.stop="handleDeleteTravelItem(index)">
                  <span class="delete-icon">×</span>
                </button>
              </div>
            </div>
          </div>
          <div v-else class="empty-travel">记录每一次旅行足迹，分享美好旅途回忆。</div>
        </div>
      </div>
    </div>

    <!-- 饮食偏好 -->
    <div class="food-section">
      <div class="section-header">
        <span class="section-icon">🍽️</span>
        <span class="section-title">饮食偏好</span>
        <div class="section-actions">
          <button class="section-tts-btn" title="朗读内容" @click="handleFoodTtsClick">
            <img src="@/assets/icon/trumpet.png" alt="朗读" class="section-tts-icon" />
          </button>
          <!-- <button class="section-mic-btn" title="语音" @click="handleFoodMicClick">
            <img src="@/assets/icon/mic.png" alt="语音" class="section-mic-icon" />
          </button> -->
          <button class="section-edit-btn" title="编辑" @click="handleFoodSectionArrowClick">
            <img src="@/assets/icon/edit.png" alt="编辑" class="section-edit-icon" />
          </button>
        </div>
      </div>
      <div class="section-content">
        <!-- 编辑模式 -->
        <div v-if="showFoodEdit" class="edit-list-container">
          <div v-for="(_, index) in foodEditValues" :key="index" class="edit-item-container">
            <div class="voice-input-wrapper">
              <!-- 麦克风按钮在多行输入框左上角 -->
              <div
                class="voice-toggle-inner"
                :class="{ breathing: isRecording && currentVoiceIndex === index }"
                @click="() => handleVoiceButtonClick('food', index)"
              >
                <i class="iconfont icon-microphone" class-prefix="icon"></i>
              </div>
              <textarea
                v-model="foodEditValues[index]"
                class="attribute-value auto-resize"
                :placeholder="`饮食偏好 ${index + 1}`"
                rows="1"
                @input="autoResize"
              ></textarea>
            </div>
          </div>
          <!-- 空白输入框用于添加新项 -->
          <div class="edit-item-container">
            <div class="voice-input-wrapper">
              <!-- 麦克风按钮在多行输入框左上角 -->
              <div
                class="voice-toggle-inner"
                :class="{ breathing: isRecording && currentVoiceIndex === -1 }"
                @click="() => handleVoiceButtonClick('food', -1)"
              >
                <i class="iconfont icon-microphone" class-prefix="icon"></i>
              </div>
              <textarea
                v-model="newFoodItem"
                class="attribute-value auto-resize"
                placeholder="添加新的饮食偏好"
                rows="1"
                @input="autoResize"
              ></textarea>
            </div>
          </div>
          <div class="edit-actions">
            <button class="save-btn" @click="handleFoodEditComplete">保存</button>
            <button class="cancel-btn" @click="handleFoodEditCancel">取消</button>
          </div>
        </div>
        <!-- 显示模式 -->
        <div v-else class="food-content">
          <div v-if="getFoodPreference()" class="food-info">
            <div v-for="(preference, index) in getFoodPreferenceList()" :key="index" class="food-item">
              <!-- 单个item编辑模式 -->
              <div v-if="editingFoodIndex === index" class="item-edit-mode">
                <div class="voice-input-wrapper">
                  <!-- 麦克风按钮在多行输入框左上角 -->
                  <div
                    class="voice-toggle-inner"
                    :class="{ breathing: isRecording && currentVoiceIndex === index }"
                    @click="() => handleVoiceButtonClick('food', index)"
                  >
                    <i class="iconfont icon-microphone" class-prefix="icon"></i>
                  </div>
                  <textarea
                    v-model="tempFoodEditValue"
                    class="item-edit-input auto-resize"
                    :placeholder="`饮食偏好 ${index + 1}`"
                    rows="1"
                    @input="autoResize"
                  ></textarea>
                </div>
                <div class="item-edit-actions">
                  <button class="item-save-btn" @click="handleFoodItemEditComplete(index)">保存</button>
                  <button class="item-cancel-btn" @click="handleFoodItemEditCancel">取消</button>
                </div>
              </div>
              <!-- 显示模式 -->
              <div v-else class="item-display-mode" @click="handleFoodItemEdit(index, preference)">
                {{ preference }}
                <button class="delete-item-btn" title="删除" @click.stop="handleDeleteFoodItem(index)">
                  <span class="delete-icon">×</span>
                </button>
              </div>
            </div>
          </div>
          <div v-else class="empty-food">了解TA的口味偏好，为下次聚餐做好准备。</div>
        </div>
      </div>
    </div>

    <!-- 我的期望 -->
    <div class="expectation-section">
      <div class="section-header">
        <span class="section-icon">🌟</span>
        <span class="section-title">我的期望</span>
        <div class="section-actions">
          <button class="section-tts-btn" title="朗读内容" @click="handleExpectationTtsClick">
            <img src="@/assets/icon/trumpet.png" alt="朗读" class="section-tts-icon" />
          </button>
          <!-- <button class="section-mic-btn" title="语音" @click="handleExpectationMicClick">
            <img src="@/assets/icon/mic.png" alt="语音" class="section-mic-icon" />
          </button> -->
          <button class="section-edit-btn" title="编辑" @click="handleExpectationSectionArrowClick">
            <img src="@/assets/icon/edit.png" alt="编辑" class="section-edit-icon" />
          </button>
        </div>
      </div>
      <div class="section-content">
        <!-- 编辑模式 -->
        <div v-if="showExpectationEdit" class="edit-list-container">
          <div v-for="(_, index) in expectationEditValues" :key="index" class="edit-item-container">
            <div class="voice-input-wrapper">
              <!-- 麦克风按钮在多行输入框左上角 -->
              <div
                class="voice-toggle-inner"
                :class="{ breathing: isRecording && currentVoiceIndex === index }"
                @click="() => handleVoiceButtonClick('expectation', index)"
              >
                <i class="iconfont icon-microphone" class-prefix="icon"></i>
              </div>
              <textarea
                :ref="(el) => setExpectationTextareaRef(el, index)"
                v-model="expectationEditValues[index]"
                class="attribute-value auto-resize"
                :placeholder="`期望 ${index + 1}`"
                rows="1"
                @input="autoResize"
              ></textarea>
            </div>
          </div>
          <!-- 空白输入框用于添加新项 -->
          <div class="edit-item-container">
            <div class="voice-input-wrapper">
              <!-- 麦克风按钮在多行输入框左上角 -->
              <div
                class="voice-toggle-inner"
                :class="{ breathing: isRecording && currentVoiceIndex === -1 }"
                @click="() => handleVoiceButtonClick('expectation', -1)"
              >
                <i class="iconfont icon-microphone" class-prefix="icon"></i>
              </div>
              <textarea
                v-model="newExpectationItem"
                class="attribute-value auto-resize"
                placeholder="添加新的期望"
                rows="1"
                @input="autoResize"
              ></textarea>
            </div>
          </div>
          <div class="edit-actions">
            <button class="save-btn" @click="handleExpectationEditComplete">保存</button>
            <button class="cancel-btn" @click="handleExpectationEditCancel">取消</button>
          </div>
        </div>
        <!-- 显示模式 -->
        <div v-else class="expectation-content">
          <div v-if="getExpectationList().length > 0" class="expectation-info">
            <div v-for="(expectation, index) in getExpectationList()" :key="index" class="expectation-item">
              <!-- 单个item编辑模式 -->
              <div v-if="editingExpectationIndex === index" class="item-edit-mode">
                <div class="voice-input-wrapper">
                  <!-- 麦克风按钮在多行输入框左上角 -->
                  <div
                    class="voice-toggle-inner"
                    :class="{ breathing: isRecording && currentVoiceIndex === index }"
                    @click="() => handleVoiceButtonClick('expectation', index)"
                  >
                    <i class="iconfont icon-microphone" class-prefix="icon"></i>
                  </div>
                  <textarea
                    v-model="tempExpectationEditValue"
                    class="item-edit-input auto-resize"
                    :placeholder="`期望 ${index + 1}`"
                    rows="1"
                    @input="autoResize"
                  ></textarea>
                </div>
                <div class="item-edit-actions">
                  <button class="item-save-btn" @click="handleExpectationItemEditComplete(index)">保存</button>
                  <button class="item-cancel-btn" @click="handleExpectationItemEditCancel">取消</button>
                </div>
              </div>
              <!-- 显示模式 -->
              <div v-else class="item-display-mode" @click="handleExpectationItemEdit(index, expectation)">
                {{ expectation }}
                <button class="delete-item-btn" title="删除" @click.stop="handleDeleteExpectationItem(index)">
                  <span class="delete-icon">×</span>
                </button>
              </div>
            </div>
          </div>
          <div v-else class="empty-expectation">记录你的期望和心愿。</div>
        </div>
      </div>
    </div>

    <!-- 其他属性信息 -->
    <div class="other-attributes-section">
      <div class="section-header">
        <span class="section-icon">📝</span>
        <span class="section-title">其他信息</span>
        <div class="section-actions">
          <button class="section-tts-btn" title="朗读内容" @click="handleOtherTtsClick">
            <img src="@/assets/icon/trumpet.png" alt="朗读" class="section-tts-icon" />
          </button>
          <button class="section-add-btn" title="添加" @click="handleAddOtherAttribute">
            <span class="add-icon">+</span>
          </button>
        </div>
      </div>
      <div class="section-content">
        <div v-if="getOtherAttributes().length > 0" class="other-attributes-content">
          <div v-for="(attr, index) in getOtherAttributes()" :key="index" class="attribute-item">
            <!-- 单个attribute编辑模式 -->
            <div v-if="editingOtherAttributeIndex === index" class="item-edit-mode">
              <div class="attribute-edit-container">
                <input
                  v-model="tempOtherAttributeEditKey"
                  type="text"
                  class="item-edit-input attribute-key-input"
                  placeholder="属性名称"
                />
                <span class="attribute-separator">:</span>
                <textarea
                  v-model="tempOtherAttributeEditValue"
                  class="item-edit-input attribute-value-input auto-resize"
                  placeholder="属性值"
                  rows="1"
                  @input="autoResize"
                ></textarea>
              </div>
              <div class="item-edit-actions">
                <button class="item-save-btn" @click="handleOtherAttributeItemEditComplete(index)">保存</button>
                <button class="item-cancel-btn" @click="handleOtherAttributeItemEditCancel">取消</button>
              </div>
            </div>
            <!-- 显示模式 -->
            <div v-else class="item-display-mode" @click="handleOtherAttributeItemEdit(index, attr.key, attr.value)">
              <div class="attribute-label">{{ attr.key }}:</div>
              <div class="attribute-value">{{ attr.value }}</div>
              <button class="delete-item-btn" title="删除" @click.stop="handleDeleteOtherAttribute(index)">
                <span class="delete-icon">×</span>
              </button>
            </div>
          </div>
        </div>
        <div v-else class="empty-other-attributes">暂无其他属性信息</div>

        <!-- 新增属性编辑模式 -->
        <div v-if="showAddOtherAttribute" class="add-attribute-mode">
          <div class="attribute-edit-container">
            <input
              v-model="newOtherAttributeKey"
              type="text"
              class="item-edit-input attribute-key-input"
              placeholder="属性名称"
            />
            <span class="attribute-separator">:</span>
            <textarea
              v-model="newOtherAttributeValue"
              class="item-edit-input attribute-value-input auto-resize"
              placeholder="属性值"
              rows="1"
              @input="autoResize"
            ></textarea>
          </div>
          <div class="item-edit-actions">
            <button class="item-save-btn" @click="handleAddOtherAttributeComplete">保存</button>
            <button class="item-cancel-btn" @click="handleAddOtherAttributeCancel">取消</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 删除确认对话框 -->
  <DeleteConfirmDialog
    :visible="showDeleteDialog"
    :content="`确定要删除这个${deleteItemInfo.type}吗？`"
    :hint="deleteItemInfo.content"
    @confirm="confirmDeleteItem"
    @cancel="closeDeleteDialog"
  />
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onBeforeUnmount } from 'vue';
import { type IPersonDetail } from '@/apis/memory';
import { updatePerson } from '@/apis/relation';
import { showSuccessToast, showFailToast, showToast } from 'vant';
import DeleteConfirmDialog from '@/components/Common/DeleteConfirmDialog.vue';
import { getStreamAsr } from '@/apis/chat';
import Recorder from 'recorder-realtime';
import { generateRandomString } from '@/utils';
import { debounce } from 'lodash-es';
import { useAudioQueue } from '@/pages/Chat/useAudioPlayer';

// Props定义
interface IProps {
  personDetail: IPersonDetail | null;
  personId: string;
  userId: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  travelSectionArrowClick: [];
  travelEditComplete: [values: string[], newItem: string];
  travelEditCancel: [];
  travelItemEdit: [index: number, value: string];
  travelItemEditComplete: [index: number, value: string];
  travelItemEditCancel: [];
  deleteTravelItem: [index: number];
  foodSectionArrowClick: [];
  foodEditComplete: [values: string[], newItem: string];
  foodEditCancel: [];
  foodItemEdit: [index: number, value: string];
  foodItemEditComplete: [index: number, value: string];
  foodItemEditCancel: [];
  deleteFoodItem: [index: number];
  expectationSectionArrowClick: [];
  expectationEditComplete: [values: string[], newItem: string];
  expectationEditCancel: [];
  expectationItemEdit: [index: number, value: string];
  expectationItemEditComplete: [index: number, value: string];
  expectationItemEditCancel: [];
  deleteExpectationItem: [index: number];
  addOtherAttribute: [];
  addOtherAttributeComplete: [key: string, value: string];
  addOtherAttributeCancel: [];
  otherAttributeItemEdit: [index: number, key: string, value: string];
  otherAttributeItemEditComplete: [index: number, key: string, value: string];
  otherAttributeItemEditCancel: [];
  deleteOtherAttribute: [index: number];
  // 修改为传递更新后的属性数据
  attributesUpdated: [newAttributes: Record<string, string>];
  // 添加语音聊天相关事件
  showVoiceChat: [sectionInfo: { title: string; icon: string; content: string }];
}>();

// TTS相关
const { play, stop, isCurrentAudioPlaying, audioStatus } = useAudioQueue();
const isTravelTtsPlaying = ref(false);
const isFoodTtsPlaying = ref(false);
const isExpectationTtsPlaying = ref(false);
const isOtherTtsPlaying = ref(false);
const travelTtsId = 'travel-section-tts';
const foodTtsId = 'food-section-tts';
const expectationTtsId = 'expectation-section-tts';
const otherTtsId = 'other-section-tts';

// 响应式数据
const showTravelEdit = ref(false);
const travelEditValues = ref<string[]>([]);
const newTravelItem = ref('');
const editingTravelIndex = ref<number | null>(null);
const tempTravelEditValue = ref('');

// 语音录音相关变量
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let timerId: ReturnType<typeof setTimeout> | null = null;
let mediaStream: MediaStream | null = null;

// 语音录音响应式数据
const micPermission = ref(false);
const sessionId = ref('');
const audioBufferIndex = ref(0);
const lastBuffer = ref();
const voiceMessage = ref('');
const isRecording = ref(false);
const currentVoiceType = ref<'travel' | 'food' | 'expectation'>('travel');
const currentVoiceIndex = ref<number>(-1);
const lastVoiceText = ref(''); // 上次语音识别的文字，用于增量更新

// Textarea refs 管理
const travelTextareaRefs = ref<(HTMLTextAreaElement | null)[]>([]);
const expectationTextareaRefs = ref<(HTMLTextAreaElement | null)[]>([]);

// 设置 textarea ref
const setTravelTextareaRef = (el: unknown, index: number) => {
  if (el && el instanceof HTMLTextAreaElement) {
    travelTextareaRefs.value[index] = el;
  }
};

const setExpectationTextareaRef = (el: unknown, index: number) => {
  if (el && el instanceof HTMLTextAreaElement) {
    expectationTextareaRefs.value[index] = el;
  }
};

// 在光标位置插入文字的工具函数
const insertTextAtCursor = (newText: string, type: 'travel' | 'expectation', index: number) => {
  let inputElement: HTMLTextAreaElement | null = null;
  let currentValue = '';

  if (type === 'travel') {
    inputElement = travelTextareaRefs.value[index];
    currentValue = travelEditValues.value[index] || '';
  } else if (type === 'expectation') {
    inputElement = expectationTextareaRefs.value[index];
    currentValue = expectationEditValues.value[index] || '';
  }

  if (!inputElement) return;

  const start = inputElement.selectionStart || 0;
  const end = inputElement.selectionEnd || 0;

  // 在光标位置插入新文字
  const newValue = currentValue.slice(0, start) + newText + currentValue.slice(end);

  if (type === 'travel') {
    travelEditValues.value[index] = newValue;
  } else if (type === 'expectation') {
    expectationEditValues.value[index] = newValue;
  }

  // 更新光标位置到插入文字的末尾
  const newCursorPosition = start + newText.length;

  // 使用 nextTick 确保 DOM 更新后再设置光标位置
  void nextTick(() => {
    inputElement!.setSelectionRange(newCursorPosition, newCursorPosition);
    inputElement!.focus();
  });
};

// 删除确认弹窗相关
const showDeleteDialog = ref(false);
const deleteItemInfo = ref<{
  type: string;
  content: string;
  category: 'travel' | 'food' | 'expectation' | 'other_attribute';
  index: number;
}>({
  type: '',
  content: '',
  category: 'travel',
  index: -1,
});

const showFoodEdit = ref(false);
const foodEditValues = ref<string[]>([]);
const newFoodItem = ref('');
const editingFoodIndex = ref<number | null>(null);
const tempFoodEditValue = ref('');

const showExpectationEdit = ref(false);
const expectationEditValues = ref<string[]>([]);
const newExpectationItem = ref('');
const editingExpectationIndex = ref<number | null>(null);
const tempExpectationEditValue = ref('');

const showAddOtherAttribute = ref(false);
const newOtherAttributeKey = ref('');
const newOtherAttributeValue = ref('');
const editingOtherAttributeIndex = ref<number | null>(null);
const tempOtherAttributeEditKey = ref('');
const tempOtherAttributeEditValue = ref('');

// 计算属性：获取处理后的key_attributes对象
const processedKeyAttributes = computed(() => {
  if (!props.personDetail || !props.personDetail.key_attributes) {
    return {};
  }

  let attributes = props.personDetail.key_attributes;

  // 如果是字符串，尝试解析为对象
  if (typeof attributes === 'string') {
    try {
      attributes = JSON.parse(attributes);
    } catch (error) {
      console.warn('⚠️ [LifestyleSection] key_attributes字符串解析失败:', error);
      return {};
    }
  }

  // 确保是对象类型
  if (typeof attributes !== 'object' || attributes === null) {
    console.warn('⚠️ [LifestyleSection] key_attributes不是有效对象:', attributes);
    return {};
  }

  // 过滤掉空值的属性
  const filteredAttributes: Record<string, string> = {};
  Object.entries(attributes).forEach(([key, value]) => {
    if (value && String(value).trim()) {
      filteredAttributes[key] = String(value);
    }
  });

  return filteredAttributes;
});

// 获取旅游历史属性值
const getTravelHistory = (): string => {
  return processedKeyAttributes.value['旅游历史'] || '';
};

// 获取旅游历史列表（分割后的数组）
const getTravelHistoryList = (): string[] => {
  const travelHistory = getTravelHistory();
  if (!travelHistory) return [];
  return travelHistory
    .split('|')
    .map((item) => item.trim())
    .filter((item) => item.length > 0);
};

// 获取餐饮偏好属性值
const getFoodPreference = (): string => {
  return processedKeyAttributes.value['餐饮偏好'] || '';
};

// 获取餐饮偏好列表（分割后的数组）
const getFoodPreferenceList = (): string[] => {
  const foodPreference = getFoodPreference();
  if (!foodPreference) return [];
  return foodPreference
    .split('|')
    .map((item) => item.trim())
    .filter((item) => item.length > 0);
};

// 去过&想去相关方法

// 语音录音相关方法
// 设置麦克风权限
async function setMicPermission() {
  try {
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
}

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    showToast('录音失败，浏览器不支持录音功能');
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  recorder.onstart = () => {};

  recorder.onstreamerror = () => {
    showToast('录音失败');
    cancelRecording();
  };

  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      const streamData = await getStreamAsr({
        sessionId: sessionId.value,
        format: 'pcm',
        sampleRate: 16000,
        index: audioBufferIndex.value,
        data: data.buffer,
      });
      if (
        streamData.data.full_text &&
        streamData.data.full_text.trim() !== '' &&
        streamData.data.full_text !== lastVoiceText.value
      ) {
        // 计算新增的文字部分
        const newText = streamData.data.full_text;
        const previousText = lastVoiceText.value;

        // 如果新文字包含之前的文字，只插入新增部分
        let textToInsert = newText;
        if (previousText && newText.startsWith(previousText)) {
          textToInsert = newText.slice(previousText.length);
        }

        // 根据当前语音类型和索引更新对应的输入框
        if (textToInsert) {
          updateVoiceInput(textToInsert);
        }

        lastVoiceText.value = newText;
        voiceMessage.value = newText;
        await autoStopTimeout();
      }
    }
  };
};

// 更新语音输入到对应的输入框
const updateVoiceInput = (text: string) => {
  if (currentVoiceType.value === 'travel') {
    if (currentVoiceIndex.value === -1) {
      newTravelItem.value += text; // 对于新项目，直接追加
    } else if (editingTravelIndex.value === currentVoiceIndex.value) {
      // 单个item编辑模式
      tempTravelEditValue.value += text;
    } else {
      // 批量编辑模式 - 在光标位置插入
      insertTextAtCursor(text, 'travel', currentVoiceIndex.value);
    }
  } else if (currentVoiceType.value === 'food') {
    if (currentVoiceIndex.value === -1) {
      newFoodItem.value += text; // 对于新项目，直接追加
    } else if (editingFoodIndex.value === currentVoiceIndex.value) {
      // 单个item编辑模式
      tempFoodEditValue.value += text;
    } else {
      // 批量编辑模式 - 直接覆盖（因为没有对应的 textarea ref）
      foodEditValues.value[currentVoiceIndex.value] = text;
    }
  } else if (currentVoiceType.value === 'expectation') {
    if (currentVoiceIndex.value === -1) {
      newExpectationItem.value += text; // 对于新项目，直接追加
    } else if (editingExpectationIndex.value === currentVoiceIndex.value) {
      // 单个item编辑模式
      tempExpectationEditValue.value += text;
    } else {
      // 批量编辑模式 - 在光标位置插入
      insertTextAtCursor(text, 'expectation', currentVoiceIndex.value);
    }
  }
};

// 开始录音
async function startRecording() {
  if (isRecording.value) {
    await stopRecording();
    return;
  }

  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    // 重置语音识别状态
    lastVoiceText.value = '';
    voiceMessage.value = '';
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopRecording();
    }, 1000 * 60);
  }
}

// 两秒不说话自动停止
const autoStopTimeout = debounce(async () => {
  await stopRecording();
}, 2000);

// 释放麦克风资源
function releaseMicrophoneResources() {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => {
      track.stop();
    });
    mediaStream = null;
    micPermission.value = false;
  }
}

// 取消录音
function cancelRecording() {
  if (recorder) {
    recorder.stop();
  }
  isRecording.value = false;
  voiceMessage.value = '';
  releaseMicrophoneResources();
}

// 结束录音
async function stopRecording() {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });
  if (voiceMessage.value) {
    // 语音识别完成，文字已经通过 insertTextAtCursor 插入到光标位置
    // 不需要再次调用 updateVoiceInput，避免覆盖用户可能的编辑
    console.log('📤 [LifestyleSection] 语音识别完成，文字已插入到光标位置:', voiceMessage.value);
  } else {
    showToast('录音解析为空，请重新录制~');
  }
  // 清空语音消息和上次识别文字
  voiceMessage.value = '';
  lastVoiceText.value = '';
}

// 处理语音按钮点击
const handleVoiceButtonClick = async (type: 'travel' | 'food' | 'expectation', index: number) => {
  currentVoiceType.value = type;
  currentVoiceIndex.value = index;
  await startRecording();
};

// TTS朗读处理 - 旅行Section
const handleTravelTtsClick = () => {
  if (isCurrentAudioPlaying(travelTtsId)) {
    stop();
    isTravelTtsPlaying.value = false;
  } else {
    // 构建朗读内容：逐条念出内部的Item内容
    const travelList = getTravelHistoryList();
    const ttsContent = travelList.length > 0 ? travelList.join('。') : '暂无旅行记录';

    if (ttsContent.trim()) {
      isTravelTtsPlaying.value = true;
      play({
        id: travelTtsId,
        text: ttsContent,
        type: 'manualPlay',
      });

      // 监听播放状态变化
      const checkPlayingStatus = () => {
        if (!isCurrentAudioPlaying(travelTtsId) && audioStatus.value === 'completed') {
          isTravelTtsPlaying.value = false;
        } else {
          setTimeout(checkPlayingStatus, 100);
        }
      };
      checkPlayingStatus();
    }
  }
};

const handleTravelSectionArrowClick = async () => {
  showTravelEdit.value = true;
  const travelList = getTravelHistoryList();
  travelEditValues.value = [...travelList];
  newTravelItem.value = '';
  emit('travelSectionArrowClick');

  // 等待 DOM 更新后自动调整所有 textarea 高度
  await nextTick();
  const textareas = document.querySelectorAll('.travel-section .edit-list-container textarea');
  textareas.forEach((textarea) => {
    autoResizeTextarea(textarea as HTMLTextAreaElement);
  });
};

const handleTravelEditComplete = async () => {
  if (!props.personDetail) return;

  try {
    // 合并现有值和新值
    const allValues = [...travelEditValues.value];
    if (newTravelItem.value.trim()) {
      allValues.push(newTravelItem.value.trim());
    }

    // 过滤空值
    const filteredValues = allValues.filter((value) => value.trim());

    // 更新属性
    const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };
    if (filteredValues.length > 0) {
      attributes['旅游历史'] = filteredValues.join('|');
    } else {
      delete attributes['旅游历史'];
    }

    await updatePersonAttributes(attributes);
    showTravelEdit.value = false;
    showSuccessToast('旅行记录保存成功');
  } catch (error) {
    console.error('保存旅行记录失败:', error);
    showFailToast('保存旅行记录失败');
  }
};

const handleTravelEditCancel = () => {
  showTravelEdit.value = false;
  travelEditValues.value = [];
  newTravelItem.value = '';
  emit('travelEditCancel');
};

const handleTravelItemEdit = async (index: number, value: string) => {
  editingTravelIndex.value = index;
  tempTravelEditValue.value = value;
  emit('travelItemEdit', index, value);

  // 等待 DOM 更新后自动调整 textarea 高度
  await nextTick();
  const textarea = document.querySelector('.travel-item .item-edit-mode textarea') as HTMLTextAreaElement;
  if (textarea) {
    autoResizeTextarea(textarea);
  }
};

const handleTravelItemEditComplete = async (index: number) => {
  if (!props.personDetail) return;

  try {
    // 获取当前旅行记录列表
    const travelList = getTravelHistoryList();

    // 更新指定索引的值
    if (index >= 0 && index < travelList.length) {
      travelList[index] = tempTravelEditValue.value.trim();
    }

    // 过滤空值
    const filteredValues = travelList.filter((value) => value.trim());

    // 更新属性
    const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };
    if (filteredValues.length > 0) {
      attributes['旅游历史'] = filteredValues.join('|');
    } else {
      delete attributes['旅游历史'];
    }

    await updatePersonAttributes(attributes);
    showSuccessToast('旅行记录保存成功');

    // 保存当前编辑的值用于事件
    const savedValue = tempTravelEditValue.value;

    // 重置编辑状态
    editingTravelIndex.value = null;
    tempTravelEditValue.value = '';
    emit('travelItemEditComplete', index, savedValue);
  } catch (error) {
    console.error('保存旅行记录失败:', error);
    showFailToast('保存旅行记录失败');
  }
};

const handleTravelItemEditCancel = () => {
  editingTravelIndex.value = null;
  tempTravelEditValue.value = '';
  emit('travelItemEditCancel');
};

const handleDeleteTravelItem = (index: number) => {
  if (!props.personDetail) return;

  const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };
  const travelHistory = attributes['旅游历史'];

  if (travelHistory) {
    const travelList = travelHistory
      .split('|')
      .map((item) => item.trim())
      .filter((item) => item);

    if (index >= 0 && index < travelList.length) {
      const itemToDelete = travelList[index];

      // 设置删除确认弹窗信息
      deleteItemInfo.value = {
        type: '旅行记录',
        content: itemToDelete,
        category: 'travel',
        index,
      };
      showDeleteDialog.value = true;
    }
  }
};

// 饮食偏好相关方法

// TTS朗读处理 - 饮食偏好Section
const handleFoodTtsClick = () => {
  if (isCurrentAudioPlaying(foodTtsId)) {
    stop();
    isFoodTtsPlaying.value = false;
  } else {
    // 构建朗读内容：逐条念出内部的Item内容
    const foodList = getFoodPreferenceList();
    const ttsContent = foodList.length > 0 ? foodList.join('。') : '暂无饮食偏好信息';

    if (ttsContent.trim()) {
      isFoodTtsPlaying.value = true;
      play({
        id: foodTtsId,
        text: ttsContent,
        type: 'manualPlay',
      });

      // 监听播放状态变化
      const checkPlayingStatus = () => {
        if (!isCurrentAudioPlaying(foodTtsId) && audioStatus.value === 'completed') {
          isFoodTtsPlaying.value = false;
        } else {
          setTimeout(checkPlayingStatus, 100);
        }
      };
      checkPlayingStatus();
    }
  }
};

const handleFoodSectionArrowClick = async () => {
  showFoodEdit.value = true;
  const foodList = getFoodPreferenceList();
  foodEditValues.value = [...foodList];
  newFoodItem.value = '';
  emit('foodSectionArrowClick');

  // 等待 DOM 更新后自动调整所有 textarea 高度
  await nextTick();
  const textareas = document.querySelectorAll('.food-section .edit-list-container textarea');
  textareas.forEach((textarea) => {
    autoResizeTextarea(textarea as HTMLTextAreaElement);
  });
};

const handleFoodEditComplete = async () => {
  if (!props.personDetail) return;

  try {
    // 合并现有值和新值
    const allValues = [...foodEditValues.value];
    if (newFoodItem.value.trim()) {
      allValues.push(newFoodItem.value.trim());
    }

    // 过滤空值
    const filteredValues = allValues.filter((value) => value.trim());

    // 更新属性
    const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };
    if (filteredValues.length > 0) {
      attributes['餐饮偏好'] = filteredValues.join('|');
    } else {
      delete attributes['餐饮偏好'];
    }

    await updatePersonAttributes(attributes);
    showFoodEdit.value = false;
    showSuccessToast('饮食偏好保存成功');
  } catch (error) {
    console.error('保存饮食偏好失败:', error);
    showFailToast('保存饮食偏好失败');
  }
};

const handleFoodEditCancel = () => {
  showFoodEdit.value = false;
  foodEditValues.value = [];
  newFoodItem.value = '';
  emit('foodEditCancel');
};

const handleFoodItemEdit = async (index: number, value: string) => {
  editingFoodIndex.value = index;
  tempFoodEditValue.value = value;
  emit('foodItemEdit', index, value);

  // 等待 DOM 更新后自动调整 textarea 高度
  await nextTick();
  const textarea = document.querySelector('.food-item .item-edit-mode textarea') as HTMLTextAreaElement;
  if (textarea) {
    autoResizeTextarea(textarea);
  }
};

const handleFoodItemEditComplete = async (index: number) => {
  if (!props.personDetail) return;

  try {
    // 获取当前饮食偏好列表
    const foodList = getFoodPreferenceList();

    // 更新指定索引的值
    if (index >= 0 && index < foodList.length) {
      foodList[index] = tempFoodEditValue.value.trim();
    }

    // 过滤空值
    const filteredValues = foodList.filter((value) => value.trim());

    // 更新属性
    const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };
    if (filteredValues.length > 0) {
      attributes['餐饮偏好'] = filteredValues.join('|');
    } else {
      delete attributes['餐饮偏好'];
    }

    await updatePersonAttributes(attributes);
    showSuccessToast('饮食偏好保存成功');

    // 保存当前编辑的值用于事件
    const savedValue = tempFoodEditValue.value;

    // 重置编辑状态
    editingFoodIndex.value = null;
    tempFoodEditValue.value = '';
    emit('foodItemEditComplete', index, savedValue);
  } catch (error) {
    console.error('保存饮食偏好失败:', error);
    showFailToast('保存饮食偏好失败');
  }
};

const handleFoodItemEditCancel = () => {
  editingFoodIndex.value = null;
  tempFoodEditValue.value = '';
  emit('foodItemEditCancel');
};

const handleDeleteFoodItem = (index: number) => {
  if (!props.personDetail) return;

  const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };
  const foodPreferences = attributes['餐饮偏好'];

  if (foodPreferences) {
    const foodList = foodPreferences
      .split('|')
      .map((item) => item.trim())
      .filter((item) => item);

    if (index >= 0 && index < foodList.length) {
      const itemToDelete = foodList[index];

      // 设置删除确认弹窗信息
      deleteItemInfo.value = {
        type: '饮食偏好',
        content: itemToDelete,
        category: 'food',
        index,
      };
      showDeleteDialog.value = true;
    }
  }
};

// 获取期望列表（从多个可能的属性中获取）
const getExpectationList = (): string[] => {
  const expectations: string[] = [];

  // 检查所有可能的期望相关属性
  const expectationKeys = ['期望', '我的期望', '期待', '愿望', '目标'];

  expectationKeys.forEach((key) => {
    const value = processedKeyAttributes.value[key];
    if (value) {
      const items = value
        .split('|')
        .map((item) => item.trim())
        .filter((item) => item.length > 0);
      expectations.push(...items);
    }
  });

  return expectations;
};

// 期望相关方法

// TTS朗读处理 - 期望Section
const handleExpectationTtsClick = () => {
  if (isCurrentAudioPlaying(expectationTtsId)) {
    stop();
    isExpectationTtsPlaying.value = false;
  } else {
    // 构建朗读内容：逐条念出内部的Item内容
    const expectationList = getExpectationList();
    const ttsContent = expectationList.length > 0 ? expectationList.join('。') : '暂无期望信息';

    if (ttsContent.trim()) {
      isExpectationTtsPlaying.value = true;
      play({
        id: expectationTtsId,
        text: ttsContent,
        type: 'manualPlay',
      });

      // 监听播放状态变化
      const checkPlayingStatus = () => {
        if (!isCurrentAudioPlaying(expectationTtsId) && audioStatus.value === 'completed') {
          isExpectationTtsPlaying.value = false;
        } else {
          setTimeout(checkPlayingStatus, 100);
        }
      };
      checkPlayingStatus();
    }
  }
};

const handleExpectationSectionArrowClick = async () => {
  showExpectationEdit.value = true;
  const expectationList = getExpectationList();
  expectationEditValues.value = [...expectationList];
  newExpectationItem.value = '';
  emit('expectationSectionArrowClick');

  // 等待 DOM 更新后自动调整所有 textarea 高度
  await nextTick();
  const textareas = document.querySelectorAll('.expectation-section .edit-list-container textarea');
  textareas.forEach((textarea) => {
    autoResizeTextarea(textarea as HTMLTextAreaElement);
  });
};

const handleExpectationEditComplete = async () => {
  if (!props.personDetail) return;

  try {
    // 合并现有值和新值
    const allValues = [...expectationEditValues.value];
    if (newExpectationItem.value.trim()) {
      allValues.push(newExpectationItem.value.trim());
    }

    // 过滤空值
    const filteredValues = allValues.filter((value) => value.trim());

    // 更新属性
    const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };

    // 查找期望相关的属性键
    const expectationKeys = ['期望', '我的期望', '期待', '愿望', '目标'];
    const foundKey = expectationKeys.find((key) => attributes[key]);
    const targetKey = foundKey || '期望'; // 默认使用'期望'

    if (filteredValues.length > 0) {
      attributes[targetKey] = filteredValues.join('|');
    } else if (foundKey) {
      // 如果有现有的期望键，删除它
      delete attributes[foundKey];
    }

    await updatePersonAttributes(attributes);
    showExpectationEdit.value = false;
    showSuccessToast('期望保存成功');
  } catch (error) {
    console.error('保存期望失败:', error);
    showFailToast('保存期望失败');
  }
};

const handleExpectationEditCancel = () => {
  showExpectationEdit.value = false;
  expectationEditValues.value = [];
  newExpectationItem.value = '';
  emit('expectationEditCancel');
};

const handleExpectationItemEdit = async (index: number, value: string) => {
  editingExpectationIndex.value = index;
  tempExpectationEditValue.value = value;
  emit('expectationItemEdit', index, value);

  // 等待 DOM 更新后自动调整 textarea 高度
  await nextTick();
  const textarea = document.querySelector('.expectation-item .item-edit-mode textarea') as HTMLTextAreaElement;
  if (textarea) {
    autoResizeTextarea(textarea);
  }
};

const handleExpectationItemEditComplete = async (index: number) => {
  if (!props.personDetail) return;

  try {
    // 获取当前期望列表
    const expectationList = getExpectationList();

    // 更新指定索引的值
    if (index >= 0 && index < expectationList.length) {
      expectationList[index] = tempExpectationEditValue.value.trim();
    }

    // 过滤空值
    const filteredValues = expectationList.filter((value) => value.trim());

    // 更新属性
    const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };

    // 查找期望相关的属性键
    const expectationKeys = ['期望', '我的期望', '期待', '愿望', '目标'];
    const foundKey = expectationKeys.find((key) => attributes[key]);
    const targetKey = foundKey || '期望'; // 默认使用'期望'

    if (filteredValues.length > 0) {
      attributes[targetKey] = filteredValues.join('|');
    } else if (foundKey) {
      // 如果有现有的期望键，删除它
      delete attributes[foundKey];
    }

    await updatePersonAttributes(attributes);
    showSuccessToast('期望保存成功');

    // 保存当前编辑的值用于事件
    const savedValue = tempExpectationEditValue.value;

    // 重置编辑状态
    editingExpectationIndex.value = null;
    tempExpectationEditValue.value = '';
    emit('expectationItemEditComplete', index, savedValue);
  } catch (error) {
    console.error('保存期望失败:', error);
    showFailToast('保存期望失败');
  }
};

const handleExpectationItemEditCancel = () => {
  editingExpectationIndex.value = null;
  tempExpectationEditValue.value = '';
  emit('expectationItemEditCancel');
};

const handleDeleteExpectationItem = (index: number) => {
  if (!props.personDetail) return;

  const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };

  // 查找期望相关的属性
  const expectationKeys = ['期望', '我的期望', '期待', '愿望', '目标'];
  const foundKey = expectationKeys.find((key) => attributes[key]);
  const expectationValue = foundKey ? attributes[foundKey] : '';

  if (expectationValue) {
    const expectationList = expectationValue
      .split('|')
      .map((item) => item.trim())
      .filter((item) => item);

    if (index >= 0 && index < expectationList.length) {
      const itemToDelete = expectationList[index];

      // 设置删除确认弹窗信息
      deleteItemInfo.value = {
        type: '期望',
        content: itemToDelete,
        category: 'expectation',
        index,
      };
      showDeleteDialog.value = true;
    }
  }
};

// 获取其他属性列表（排除已知的特殊属性）
const getOtherAttributes = (): Array<{ key: string; value: string }> => {
  const knownKeys = ['当前城市', '旅游历史', '餐饮偏好', '期望', '我的期望', '期待', '愿望', '目标'];

  const otherAttributes: Array<{ key: string; value: string }> = [];

  Object.entries(processedKeyAttributes.value).forEach(([key, value]) => {
    if (!knownKeys.includes(key) && value && value.trim()) {
      otherAttributes.push({ key, value });
    }
  });

  return otherAttributes;
};

// TTS朗读处理 - 其他信息Section
const handleOtherTtsClick = () => {
  if (isCurrentAudioPlaying(otherTtsId)) {
    stop();
    isOtherTtsPlaying.value = false;
  } else {
    // 构建朗读内容：逐条念出内部的Item内容
    const otherAttributes = getOtherAttributes();
    const ttsContent =
      otherAttributes.length > 0
        ? otherAttributes.map((attr) => `${attr.key}：${attr.value}`).join('。')
        : '暂无其他属性信息';

    if (ttsContent.trim()) {
      isOtherTtsPlaying.value = true;
      play({
        id: otherTtsId,
        text: ttsContent,
        type: 'manualPlay',
      });

      // 监听播放状态变化
      const checkPlayingStatus = () => {
        if (!isCurrentAudioPlaying(otherTtsId) && audioStatus.value === 'completed') {
          isOtherTtsPlaying.value = false;
        } else {
          setTimeout(checkPlayingStatus, 100);
        }
      };
      checkPlayingStatus();
    }
  }
};

// 其他属性相关方法
const handleAddOtherAttribute = async () => {
  showAddOtherAttribute.value = true;
  newOtherAttributeKey.value = '';
  newOtherAttributeValue.value = '';
  emit('addOtherAttribute');

  // 等待 DOM 更新后自动调整 textarea 高度
  await nextTick();
  const textarea = document.querySelector('.add-attribute-mode textarea') as HTMLTextAreaElement;
  if (textarea) {
    autoResizeTextarea(textarea);
  }
};

const handleAddOtherAttributeComplete = async () => {
  if (!props.personDetail) return;

  try {
    const newKey = newOtherAttributeKey.value.trim();
    const newValue = newOtherAttributeValue.value.trim();

    // 检查键名和值是否都不为空
    if (!newKey) {
      showFailToast('请输入属性名称');
      return;
    }

    // 更新属性
    const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };

    // 只有当值不为空时才添加属性
    if (newValue) {
      attributes[newKey] = newValue;

      await updatePersonAttributes(attributes);
      showSuccessToast('属性添加成功');

      // 重置状态
      showAddOtherAttribute.value = false;
      newOtherAttributeKey.value = '';
      newOtherAttributeValue.value = '';
      emit('addOtherAttributeComplete', newKey, newValue);
    } else {
      showFailToast('请输入属性值');
    }
  } catch (error) {
    console.error('添加属性失败:', error);
    showFailToast('添加属性失败');
  }
};

const handleAddOtherAttributeCancel = () => {
  showAddOtherAttribute.value = false;
  newOtherAttributeKey.value = '';
  newOtherAttributeValue.value = '';
  emit('addOtherAttributeCancel');
};

const handleOtherAttributeItemEdit = async (index: number, key: string, value: string) => {
  editingOtherAttributeIndex.value = index;
  tempOtherAttributeEditKey.value = key;
  tempOtherAttributeEditValue.value = value;
  emit('otherAttributeItemEdit', index, key, value);

  // 等待 DOM 更新后自动调整 textarea 高度
  await nextTick();
  const textarea = document.querySelector('.attribute-item .item-edit-mode textarea') as HTMLTextAreaElement;
  if (textarea) {
    autoResizeTextarea(textarea);
  }
};

const handleOtherAttributeItemEditComplete = async (index: number) => {
  if (!props.personDetail) return;

  try {
    // 获取其他属性列表
    const otherAttributes = getOtherAttributes();

    // 更新指定索引的属性
    if (index >= 0 && index < otherAttributes.length) {
      const oldKey = otherAttributes[index].key;
      const newKey = tempOtherAttributeEditKey.value.trim();
      const newValue = tempOtherAttributeEditValue.value.trim();

      // 更新属性
      const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };

      // 如果键名改变了，删除旧的键
      if (oldKey !== newKey) {
        delete attributes[oldKey];
      }

      // 设置新的键值对（如果值不为空）
      if (newKey && newValue) {
        attributes[newKey] = newValue;
      } else if (newKey && !newValue) {
        // 如果只有键没有值，也删除这个属性
        delete attributes[newKey];
      }

      await updatePersonAttributes(attributes);
      showSuccessToast('属性保存成功');

      // 重置编辑状态
      editingOtherAttributeIndex.value = null;
      tempOtherAttributeEditKey.value = '';
      tempOtherAttributeEditValue.value = '';
      emit('otherAttributeItemEditComplete', index, newKey, newValue);
    }
  } catch (error) {
    console.error('保存属性失败:', error);
    showFailToast('保存属性失败');
  }
};

const handleOtherAttributeItemEditCancel = () => {
  editingOtherAttributeIndex.value = null;
  tempOtherAttributeEditKey.value = '';
  tempOtherAttributeEditValue.value = '';
  emit('otherAttributeItemEditCancel');
};

const handleDeleteOtherAttribute = (index: number) => {
  if (!props.personDetail) return;

  const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };
  const knownKeys = ['当前城市', '旅游历史', '餐饮偏好', '期望', '我的期望', '期待', '愿望', '目标'];

  // 获取其他属性列表
  const otherAttributes: Array<{ key: string; value: string }> = [];
  Object.entries(attributes).forEach(([key, value]) => {
    if (!knownKeys.includes(key) && value && value.trim()) {
      otherAttributes.push({ key, value });
    }
  });

  if (index >= 0 && index < otherAttributes.length) {
    const attributeToDelete = otherAttributes[index];

    // 设置删除确认弹窗信息
    deleteItemInfo.value = {
      type: '属性',
      content: `${attributeToDelete.key}: ${attributeToDelete.value}`,
      category: 'other_attribute',
      index,
    };
    showDeleteDialog.value = true;
  }
};

// 关闭删除确认对话框
const closeDeleteDialog = () => {
  showDeleteDialog.value = false;
  deleteItemInfo.value = {
    type: '',
    content: '',
    category: 'travel',
    index: -1,
  };
};

// 确认删除Item
const confirmDeleteItem = async () => {
  if (!props.personDetail || deleteItemInfo.value.index === -1) return;

  try {
    const attributes = { ...(props.personDetail.key_attributes as Record<string, string>) };
    const { category, index } = deleteItemInfo.value;

    if (category === 'travel') {
      // 删除旅行记录
      const travelHistory = attributes['旅游历史'];
      if (travelHistory) {
        const travelList = travelHistory
          .split('|')
          .map((item) => item.trim())
          .filter((item) => item);
        travelList.splice(index, 1);

        if (travelList.length > 0) {
          attributes['旅游历史'] = travelList.join('|');
        } else {
          delete attributes['旅游历史'];
        }
      }
    } else if (category === 'food') {
      // 删除饮食偏好
      const foodPreferences = attributes['餐饮偏好'];
      if (foodPreferences) {
        const foodList = foodPreferences
          .split('|')
          .map((item) => item.trim())
          .filter((item) => item);
        foodList.splice(index, 1);

        if (foodList.length > 0) {
          attributes['餐饮偏好'] = foodList.join('|');
        } else {
          delete attributes['餐饮偏好'];
        }
      }
    } else if (category === 'expectation') {
      // 删除期望
      const expectationKeys = ['期望', '我的期望', '期待', '愿望', '目标'];
      const foundKey = expectationKeys.find((key) => attributes[key]);
      const targetKey = foundKey || '';
      const expectationValue = foundKey ? attributes[foundKey] : '';

      if (targetKey && expectationValue) {
        const expectationList = expectationValue
          .split('|')
          .map((item) => item.trim())
          .filter((item) => item);
        expectationList.splice(index, 1);

        if (expectationList.length > 0) {
          attributes[targetKey] = expectationList.join('|');
        } else {
          delete attributes[targetKey];
        }
      }
    } else if (category === 'other_attribute') {
      // 删除其他属性
      const knownKeys = ['当前城市', '旅游历史', '餐饮偏好', '期望', '我的期望', '期待', '愿望', '目标'];

      // 获取其他属性列表
      const otherAttributes: Array<{ key: string; value: string }> = [];
      Object.entries(attributes).forEach(([key, value]) => {
        if (!knownKeys.includes(key) && value && value.trim()) {
          otherAttributes.push({ key, value });
        }
      });

      if (index >= 0 && index < otherAttributes.length) {
        const keyToDelete = otherAttributes[index].key;
        delete attributes[keyToDelete];
      }
    }

    // 调用API更新人员属性
    await updatePersonAttributes(attributes);
    showSuccessToast('删除成功');
  } catch (error) {
    console.error('删除失败:', error);
    showFailToast('删除失败');
  } finally {
    // 关闭弹窗
    closeDeleteDialog();
  }
};

// 更新人员属性的通用函数
const updatePersonAttributes = async (newAttributes: Record<string, string>) => {
  if (!props.personDetail) return;

  const aliases = props.personDetail.aliases || '';
  const submitAliases = aliases === '' ? '' : aliases;

  const response = await updatePerson(props.personDetail.person_id, {
    user_id: props.userId,
    canonical_name: props.personDetail.canonical_name,
    aliases: submitAliases,
    relationships: props.personDetail.relationships as string[],
    profile_summary: props.personDetail.profile_summary,
    key_attributes: newAttributes,
    is_user: props.personDetail.is_user,
    avatar: props.personDetail.avatar,
  });

  if (response && response.result === 'success') {
    // 通知父组件属性已更新
    emit('attributesUpdated', newAttributes);
  } else {
    throw new Error('更新失败');
  }
};

// 自动调整 textarea 高度
const autoResize = (event: Event) => {
  const target = event.target as HTMLTextAreaElement;
  if (target) {
    target.style.height = 'auto';
    target.style.height = `${target.scrollHeight}px`;
  }
};

// 根据内容自动调整指定 textarea 的高度
const autoResizeTextarea = (textarea: HTMLTextAreaElement) => {
  if (textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = `${textarea.scrollHeight}px`;
  }
};

// 组件卸载时释放麦克风资源
onBeforeUnmount(() => {
  if (isRecording.value) {
    if (recorder) {
      recorder.stop();
    }
    isRecording.value = false;
  }
  releaseMicrophoneResources();
});
</script>

<style lang="scss" scoped>
.lifestyle-container {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.travel-section,
.food-section,
.expectation-section,
.other-attributes-section {
  border: none;
  border-radius: 16px;
  padding: 22px;
  margin-top: 24px;
  background: rgba(0, 188, 212, 0.05);
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;

  .section-icon {
    font-size: 32px;
  }

  .section-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 34px;
    font-weight: 600;
    flex: 1;
  }

  .section-actions {
    display: flex;
    align-items: center;
    gap: 14px;
  }

  .section-edit-btn,
  .section-mic-btn,
  .section-add-btn,
  .section-tts-btn {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: var(--primary-color-light);
      transform: translateY(-2px);
      box-shadow: var(--shadow-accent);
    }

    .section-edit-icon,
    .section-mic-icon,
    .section-tts-icon {
      width: 18px;
      height: 18px;
      filter: var(--icon-filter-primary);
    }

    .add-icon {
      font-size: 20px;
      font-weight: bold;
      color: var(--primary-color);
    }
  }
}

.section-content {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

// 编辑模式样式
.edit-list-container {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .edit-item-container {
    .voice-input-wrapper {
      position: relative;
      width: 100%;

      .voice-toggle-inner {
        position: absolute;
        left: 16px;
        top: 16px;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 50%;
        background: var(--bg-glass);
        border: 2px solid var(--border-accent);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
        z-index: 10;

        &.breathing {
          animation: breathing 2s ease-in-out infinite;
        }

        .iconfont {
          font-size: 20px;
          color: var(--text-primary);
        }
      }

      .attribute-value {
        width: 100%;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        padding: 14px 16px 14px 70px; // 左侧留出空间给语音按钮
        color: rgba(255, 255, 255, 0.9);
        font-size: 32px;
        box-sizing: border-box;
        backdrop-filter: blur(10px);
        transition: all 0.2s ease;
        resize: none;
        min-height: 60px;
        font-family: inherit;
        line-height: 1.4;
        overflow: hidden;

        &.auto-resize {
          resize: none;
          overflow: hidden;
        }

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        &:focus {
          outline: none;
          border-color: rgba(255, 255, 255, 0.5);
          background: rgba(255, 255, 255, 0.15);
          box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
        }
      }
    }
  }

  .edit-actions {
    display: flex;
    gap: 12px;
    margin-top: 16px;

    .save-btn,
    .cancel-btn {
      padding: 12px 24px;
      border-radius: 12px;
      font-size: 28px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid;

      &.save-btn {
        background: var(--primary-color-medium);
        border-color: var(--primary-color);
        color: var(--primary-color);

        &:hover {
          background: var(--primary-color-strong);
          transform: translateY(-2px);
          box-shadow: var(--shadow-accent);
        }
      }

      &.cancel-btn {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.8);

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
        }
      }
    }
  }
}

// 显示模式样式
.travel-content,
.food-content,
.expectation-content,
.other-attributes-content {
  .travel-info,
  .food-info,
  .expectation-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
}

// 其他属性内容间距统一
.other-attributes-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

// 所有item样式
.travel-content,
.food-content,
.expectation-content,
.other-attributes-content {
  .travel-item,
  .food-item,
  .expectation-item,
  .attribute-item {
    background: var(--primary-color-light);
    border: 2px solid var(--border-accent);
    border-radius: 16px;
    padding: 16px;
    position: relative;
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(0, 188, 212, 0.5);
      background: rgba(0, 188, 212, 0.08);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.2);
    }

    .item-display-mode {
      cursor: pointer;
      font-size: 32px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.9);
      padding-right: 40px;
      display: flex;
      align-items: center;
      gap: 8px;

      .attribute-label {
        color: var(--primary-color);
        font-weight: 700;
      }

      .attribute-value {
        flex: 1;
      }
    }

    .delete-item-btn {
      position: absolute;
      top: 4px;
      right: 4px;
      background: none;
      border: none;
      cursor: pointer;
      color: rgba(255, 255, 255, 0.6);
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.2s ease;
      font-size: 18px;
      line-height: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        color: var(--error-color);
        transform: scale(1.1);
      }

      .delete-icon {
        font-size: 30px;
      }
    }
  }
}

// 空状态样式
.empty-travel,
.empty-food,
.empty-expectation,
.empty-other-attributes {
  color: rgba(255, 255, 255, 0.6);
  font-size: 30px;
  font-style: italic;
  text-align: center;
  padding: 20px 0;
  background: var(--primary-color-light);
  border: 1px dashed var(--border-accent);
  border-radius: 12px;
  line-height: 1.6;
}

// 单个item编辑模式样式
.item-edit-mode {
  .voice-input-wrapper {
    position: relative;
    width: 100%;

    .voice-toggle-inner {
      position: absolute;
      left: 16px;
      top: 16px;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;
      background: var(--bg-glass);
      border: 2px solid var(--border-accent);
      backdrop-filter: blur(20px);
      transition: all 0.3s ease;
      z-index: 10;

      &.breathing {
        animation: breathing 2s ease-in-out infinite;
      }

      .iconfont {
        font-size: 20px;
        color: var(--text-primary);
      }
    }

    .item-edit-input {
      width: 100%;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 12px;
      padding: 14px 16px 14px 70px; // 左侧留出空间给语音按钮
      color: rgba(255, 255, 255, 0.9);
      font-size: 32px;
      box-sizing: border-box;
      backdrop-filter: blur(10px);
      transition: all 0.2s ease;
      margin-bottom: 12px;
      resize: none;
      min-height: 60px;
      font-family: inherit;
      line-height: 1.4;
      overflow: hidden;

      &.auto-resize {
        resize: none;
        overflow: hidden;
      }

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
      }
    }
  }

  // 兼容没有语音输入包装器的旧样式
  .item-edit-input:not(.voice-input-wrapper .item-edit-input) {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 14px 16px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 32px;
    box-sizing: border-box;
    backdrop-filter: blur(10px);
    transition: all 0.2s ease;
    margin-bottom: 12px;
    resize: none;
    min-height: 60px;
    font-family: inherit;
    line-height: 1.4;
    overflow: hidden;

    &.auto-resize {
      resize: none;
      overflow: hidden;
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.5);
    }

    &:focus {
      outline: none;
      border-color: rgba(255, 255, 255, 0.5);
      background: rgba(255, 255, 255, 0.15);
      box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
    }
  }

  .item-edit-actions {
    display: flex;
    gap: 12px;

    .item-save-btn,
    .item-cancel-btn {
      padding: 12px 24px;
      border-radius: 12px;
      font-size: 28px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid;

      &.item-save-btn {
        background: var(--primary-color-medium);
        border-color: var(--primary-color);
        color: var(--primary-color);

        &:hover {
          background: var(--primary-color-strong);
          transform: translateY(-2px);
          box-shadow: var(--shadow-accent);
        }
      }

      &.item-cancel-btn {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.8);

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
        }
      }
    }
  }
}

// 其他属性特殊样式
.add-attribute-mode {
  background: rgba(0, 188, 212, 0.05);
  border: 2px solid rgba(0, 188, 212, 0.3);
  border-radius: 16px;
  padding: 16px;
  margin-top: 12px;

  .attribute-edit-container {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;

    .attribute-key-input {
      flex: 1;
      min-width: 120px;
    }

    .attribute-separator {
      color: var(--primary-color);
      font-size: 32px;
      font-weight: bold;
    }

    .attribute-value-input {
      flex: 2;
      min-width: 200px;
    }

    .item-edit-input {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 12px;
      padding: 14px 16px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 32px;
      box-sizing: border-box;
      backdrop-filter: blur(10px);
      transition: all 0.2s ease;
      resize: none;
      min-height: 60px;
      font-family: inherit;
      line-height: 1.4;
      overflow: hidden;

      &.auto-resize {
        resize: none;
        overflow: hidden;
      }

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
      }
    }
  }

  .item-edit-actions {
    display: flex;
    gap: 12px;

    .item-save-btn,
    .item-cancel-btn {
      padding: 12px 24px;
      border-radius: 12px;
      font-size: 28px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid;

      &.item-save-btn {
        background: var(--primary-color-medium);
        border-color: var(--primary-color);
        color: var(--primary-color);

        &:hover {
          background: var(--primary-color-strong);
          transform: translateY(-2px);
          box-shadow: var(--shadow-accent);
        }
      }

      &.item-cancel-btn {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.8);

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
        }
      }
    }
  }
}

// 其他属性item编辑模式特殊样式
.attribute-item .item-edit-mode .attribute-edit-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;

  .attribute-key-input {
    flex: 1;
    min-width: 120px;
  }

  .attribute-separator {
    color: var(--primary-color);
    font-size: 32px;
    font-weight: bold;
  }

  .attribute-value-input {
    flex: 2;
    min-width: 200px;
  }
}

@keyframes breathing {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 255, 255, 0.6);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 8px rgba(0, 255, 255, 0.2);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 255, 255, 0.6);
  }
}
</style>
